# MongoDB Setup Guide for MATERNIFY

This guide provides instructions for setting up MongoDB for the MATERNIFY application.

## Option 1: Local MongoDB Setup

### Prerequisites
- MongoDB Community Server installed on your machine
  - Download from: https://www.mongodb.com/try/download/community

### Steps
1. Install MongoDB Community Server
2. Start the MongoDB service:
   - Windows: MongoDB should run as a service automatically
   - macOS: `brew services start mongodb-community`
   - Linux: `sudo systemctl start mongod`
3. Verify MongoDB is running:
   - Connect to MongoDB shell: `mongosh`
   - You should see a connection message and a prompt
4. The application is already configured to connect to a local MongoDB instance at `mongodb://localhost:27017/maternify`

## Option 2: MongoDB Atlas Setup (Cloud)

### Steps
1. Create a MongoDB Atlas account:
   - Go to https://www.mongodb.com/cloud/atlas/register
   - Sign up for a free account

2. Create a new cluster:
   - Choose the free tier (M0)
   - Select a cloud provider and region close to your users
   - Name your cluster (e.g., "maternify-cluster")

3. Set up database access:
   - Go to "Database Access" in the security section
   - Add a new database user with a username and password
   - Give the user "Read and write to any database" permissions

4. Set up network access:
   - Go to "Network Access" in the security section
   - Add a new IP address
   - For development, you can allow access from anywhere (0.0.0.0/0)
   - For production, restrict to specific IP addresses

5. Get your connection string:
   - Go to "Clusters" and click "Connect"
   - Choose "Connect your application"
   - Select Node.js as the driver
   - Copy the connection string

6. Update your .env file:
   - Open the `.env` file in the server directory
   - Replace the MONGODB_URI value with your connection string
   - Replace `<username>`, `<password>`, and `<clustername>` with your actual values

Example connection string:
```
MONGODB_URI=mongodb+srv://<username>:<password>@<clustername>.mongodb.net/maternify?retryWrites=true&w=majority
```

## Testing Your MongoDB Connection

To test your MongoDB connection:

1. Navigate to the server directory:
   ```
   cd server
   ```

2. Run the connection test script:
   ```
   node mongodb-connection-test.js
   ```

3. If successful, you should see:
   ```
   ✅ MongoDB connection successful!
   ```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Make sure MongoDB is running
   - Check if the port is correct (default: 27017)

2. **Authentication Failed**
   - Check your username and password
   - Ensure the user has the correct permissions

3. **Network Access Issues**
   - For MongoDB Atlas, make sure your IP is whitelisted
   - Check your firewall settings

4. **Invalid Connection String**
   - Verify the format of your connection string
   - Make sure all placeholders are replaced with actual values

### Getting Help

If you continue to experience issues, please:
1. Check the MongoDB documentation: https://docs.mongodb.com/
2. For MongoDB Atlas issues: https://docs.atlas.mongodb.com/
3. Contact the project maintainers for specific application-related questions
