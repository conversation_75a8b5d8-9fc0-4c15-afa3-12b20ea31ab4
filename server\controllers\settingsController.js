const Settings = require('../models/Settings');
const Activity = require('../models/Activity');

// Get all settings (admin only)
exports.getAllSettings = async (req, res, next) => {
  try {
    // Filter by group if provided
    let query = {};
    if (req.query.group) {
      query.group = req.query.group;
    }

    const settings = await Settings.find(query).sort('key');

    res.status(200).json({
      success: true,
      count: settings.length,
      data: settings,
    });
  } catch (error) {
    next(error);
  }
};

// Get setting by key
exports.getSettingByKey = async (req, res, next) => {
  try {
    const setting = await Settings.findOne({ key: req.params.key });

    if (!setting) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found',
      });
    }

    res.status(200).json({
      success: true,
      data: setting,
    });
  } catch (error) {
    next(error);
  }
};

// Update setting (admin only)
exports.updateSetting = async (req, res, next) => {
  try {
    // Add updatedBy to request body
    req.body.updatedBy = req.user.id;
    req.body.updatedAt = Date.now();

    const setting = await Settings.findOneAndUpdate(
      { key: req.params.key },
      req.body,
      {
        new: true,
        runValidators: true,
        upsert: true, // Create if doesn't exist
      }
    );

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'settings_update',
      details: `Setting updated: ${req.params.key}`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(200).json({
      success: true,
      data: setting,
    });
  } catch (error) {
    next(error);
  }
};

// Update multiple settings (admin only)
exports.updateMultipleSettings = async (req, res, next) => {
  try {
    const { settings } = req.body;

    if (!settings || !Array.isArray(settings)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an array of settings',
      });
    }

    const updatedSettings = [];

    // Update each setting
    for (const setting of settings) {
      if (!setting.key) {
        continue;
      }

      setting.updatedBy = req.user.id;
      setting.updatedAt = Date.now();

      const updatedSetting = await Settings.findOneAndUpdate(
        { key: setting.key },
        setting,
        {
          new: true,
          runValidators: true,
          upsert: true, // Create if doesn't exist
        }
      );

      updatedSettings.push(updatedSetting);
    }

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'settings_update',
      details: `Multiple settings updated: ${updatedSettings.length} settings`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(200).json({
      success: true,
      count: updatedSettings.length,
      data: updatedSettings,
    });
  } catch (error) {
    next(error);
  }
};
