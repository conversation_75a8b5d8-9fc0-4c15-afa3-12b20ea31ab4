<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password | MATERNIFY</title>
    <meta name="description" content="Create a new password for your MATERNIFY account to regain access to your personalized pregnancy and infant care tools.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                    <li><a href="/infant-care">Infant Care</a></li>
                    <li><a href="/tools">Tools</a></li>
                    <li><a href="/community">Community</a></li>
                    <li><a href="/blog">Articles</a></li>
                    <li><a href="/login" class="btn-login">Login</a></li>
                    <li><a href="/register" class="btn-register">Register</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-section" style="padding: var(--spacing-xxl) 0; background-color: var(--light-bg);">
        <div class="container">
            <div class="auth-container">
                <div class="auth-header">
                    <h1>Reset Password</h1>
                    <p>Create a new password for your account</p>
                </div>

                <form class="auth-form" id="resetPasswordForm">
                    <div class="form-group">
                        <label for="password">New Password</label>
                        <div class="password-field">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                <i class="far fa-eye"></i>
                            </button>
                        </div>
                        <div class="error-message" id="passwordError" style="display: none;">Password must be at least 8 characters with letters and numbers</div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm New Password</label>
                        <div class="password-field">
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                            <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                <i class="far fa-eye"></i>
                            </button>
                        </div>
                        <div class="error-message" id="confirmPasswordError" style="display: none;">Passwords do not match</div>
                    </div>

                    <button type="submit" class="btn-submit">Reset Password</button>
                </form>

                <div id="resetSuccess" style="display: none; margin-top: 20px; text-align: center; padding: 15px; background-color: #d4edda; color: #155724; border-radius: 5px;">
                    <p>Your password has been reset successfully. You can now <a href="/login" style="color: #155724; text-decoration: underline;">log in</a> with your new password.</p>
                </div>

                <div class="auth-switch" style="margin-top: 20px;">
                    <a href="/login">Back to Login</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                        <li><a href="/infant-care">Infant Care</a></li>
                        <li><a href="/tools">Tools</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/blog">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/tools#due-date">Due Date Calculator</a></li>
                        <li><a href="/tools#bmi">BMI Tracker</a></li>
                        <li><a href="/tools#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/tools#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const resetPasswordForm = document.getElementById('resetPasswordForm');
            const resetSuccess = document.getElementById('resetSuccess');
            
            // Get token from URL
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');
            
            if (!token) {
                alert('Invalid or missing reset token. Please request a new password reset link.');
                window.location.href = '/forgot-password';
                return;
            }
            
            if (resetPasswordForm) {
                resetPasswordForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    const passwordError = document.getElementById('passwordError');
                    const confirmPasswordError = document.getElementById('confirmPasswordError');
                    
                    let isValid = true;
                    
                    // Validate password
                    if (!password || password.length < 8) {
                        passwordError.style.display = 'block';
                        isValid = false;
                    } else {
                        passwordError.style.display = 'none';
                    }
                    
                    // Validate password confirmation
                    if (password !== confirmPassword) {
                        confirmPasswordError.style.display = 'block';
                        isValid = false;
                    } else {
                        confirmPasswordError.style.display = 'none';
                    }
                    
                    if (!isValid) return;
                    
                    // Show loading state
                    const submitButton = resetPasswordForm.querySelector('.btn-submit');
                    const originalText = submitButton.textContent;
                    submitButton.textContent = 'Resetting...';
                    submitButton.disabled = true;
                    
                    try {
                        // Check if API service is available
                        if (!window.apiService) {
                            console.error('API service not found. Make sure api-service.js is loaded before this script');
                            alert('An error occurred. Please try again later.');
                            
                            // Reset button
                            submitButton.textContent = originalText;
                            submitButton.disabled = false;
                            return;
                        }
                        
                        // Call the reset password API
                        const result = await apiService.resetPassword(token, password);
                        
                        if (result.success) {
                            // Show success message
                            resetPasswordForm.style.display = 'none';
                            resetSuccess.style.display = 'block';
                        } else {
                            // Show error message
                            alert(result.message || 'An error occurred. Please try again.');
                            
                            // Reset button
                            submitButton.textContent = originalText;
                            submitButton.disabled = false;
                        }
                    } catch (error) {
                        console.error('Reset password error:', error);
                        alert('An error occurred. Please try again.');
                        
                        // Reset button
                        submitButton.textContent = originalText;
                        submitButton.disabled = false;
                    }
                });
            }
            
            // Password visibility toggle
            const toggleButtons = document.querySelectorAll('.toggle-password');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const passwordField = this.previousElementSibling;
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);
                    
                    // Toggle icon
                    const icon = this.querySelector('i');
                    icon.classList.toggle('fa-eye');
                    icon.classList.toggle('fa-eye-slash');
                });
            });
        });
    </script>
</body>
</html>
