const mongoose = require('mongoose');
const fs = require('fs');

const connectDB = async () => {
  try {
    fs.appendFileSync('server-log.txt', 'Attempting to connect to MongoDB Atlas...\n');

    // Check if MONGODB_URI is set
    if (!process.env.MONGODB_URI) {
      throw new Error('MongoDB connection string is not defined in environment variables');
    }

    // Connect to MongoDB
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    const successMsg = `MongoDB Connected: ${conn.connection.host}`;
    console.log(successMsg);
    fs.appendFileSync('server-log.txt', successMsg + '\n');

    // Indexes will be created automatically by MongoDB

    return true;
  } catch (error) {
    const errorMsg = `Error connecting to MongoDB: ${error.message}`;
    console.error(errorMsg);
    fs.appendFileSync('server-log.txt', errorMsg + '\n');

    // Don't exit the process, just return false
    return false;
  }
};

module.exports = connectDB;
