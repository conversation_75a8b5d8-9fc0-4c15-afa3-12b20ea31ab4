try {
  console.log('Loading express...');
  const express = require('express');
  console.log('Express loaded successfully');

  const app = express();
  const PORT = 8080;

  app.get('/', (req, res) => {
    res.send('Express server is working!');
  });

  try {
    console.log('Starting server...');
    app.listen(PORT, () => {
      console.log(`Test server running on http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('Error starting server:', error);
  }
} catch (error) {
  console.error('Error loading express:', error);
}
