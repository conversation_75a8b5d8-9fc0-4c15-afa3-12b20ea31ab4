{"name": "maternify-server", "version": "1.0.0", "description": "Backend server for MATERNIFY pregnancy and infant care platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node config/seedData.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "morgan": "^1.10.0", "validator": "^13.9.0"}, "devDependencies": {"nodemon": "^2.0.22"}}