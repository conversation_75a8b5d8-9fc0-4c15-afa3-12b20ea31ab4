const mongoose = require('mongoose');

const systemLogSchema = new mongoose.Schema({
  level: {
    type: String,
    required: [true, 'Log level is required'],
    enum: ['info', 'warning', 'error'],
  },
  message: {
    type: String,
    required: [true, 'Log message is required'],
  },
  source: {
    type: String,
  },
  stack: {
    type: String,
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
});

// Index for faster queries
systemLogSchema.index({ timestamp: -1 });
systemLogSchema.index({ level: 1, timestamp: -1 });

const SystemLog = mongoose.model('SystemLog', systemLogSchema);

module.exports = SystemLog;
