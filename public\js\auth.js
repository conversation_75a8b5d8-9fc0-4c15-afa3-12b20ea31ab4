// Authentication handling for MATERNIFY

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded');

    // Check if API service is available
    if (!window.apiService) {
        console.error('API service not found. Make sure api-service.js is loaded before auth.js');
        return;
    }

    // Check if user is logged in
    checkAuthStatus();

    // Login form handling
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get form values
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            // Validate form
            let isValid = true;

            // Email validation
            if (!email || !isValidEmail(email)) {
                document.getElementById('emailError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('emailError').style.display = 'none';
            }

            // Password validation
            if (!password) {
                document.getElementById('passwordError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('passwordError').style.display = 'none';
            }

            if (isValid) {
                // Show loading state
                const submitButton = loginForm.querySelector('.btn-submit');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Logging in...';
                submitButton.disabled = true;

                try {
                    // Call the login API using the API service
                    const result = await apiService.login(email, password);

                    if (result.success) {
                        // Show success message
                        alert('Login successful! Redirecting to your dashboard.');

                        // Redirect to dashboard page without .html extension
                        window.location.href = '/dashboard';
                    } else {
                        // Show error message
                        alert(result.message || 'Login failed. Please check your credentials and try again.');

                        // Reset button
                        submitButton.textContent = originalText;
                        submitButton.disabled = false;
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    alert('An error occurred during login. Please try again.');

                    // Reset button
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            }
        });
    }

    // Registration form handling
    const registerForm = document.getElementById('registerForm');
    console.log('Register form:', registerForm);

    if (registerForm) {
        console.log('Adding submit event listener to register form');
        registerForm.addEventListener('submit', async function(e) {
            console.log('Register form submitted');
            e.preventDefault();

            // Get form values
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Validate form
            let isValid = true;

            // First name validation
            if (!firstName) {
                document.getElementById('firstNameError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('firstNameError').style.display = 'none';
            }

            // Last name validation
            if (!lastName) {
                document.getElementById('lastNameError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('lastNameError').style.display = 'none';
            }

            // Email validation
            if (!email || !isValidEmail(email)) {
                document.getElementById('emailError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('emailError').style.display = 'none';
            }

            // Password validation
            if (!password) {
                document.getElementById('passwordError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('passwordError').style.display = 'none';
            }

            // Confirm password validation
            if (password !== confirmPassword) {
                document.getElementById('confirmPasswordError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('confirmPasswordError').style.display = 'none';
            }

            if (isValid) {
                // Show loading state
                const submitButton = registerForm.querySelector('.btn-submit');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Creating account...';
                submitButton.disabled = true;

                try {
                    console.log('Calling register API with:', { firstName, lastName, email, password: '***' });

                    // Call the register API using the API service
                    const result = await apiService.register({
                        firstName,
                        lastName,
                        email,
                        password
                    });

                    console.log('Register API response:', result);

                    if (result.success) {
                        // Show success message
                        alert('Registration successful! Redirecting to login page.');

                        // Redirect to login page with query parameters to show success message
                        window.location.href = `/login?registered=true&email=${encodeURIComponent(email)}`;
                    } else {
                        // Show error message
                        alert(result.message || 'Registration failed. Please try again.');

                        // Reset button
                        submitButton.textContent = originalText;
                        submitButton.disabled = false;
                    }
                } catch (error) {
                    console.error('Registration error:', error);
                    alert('An error occurred during registration. Please try again.');

                    // Reset button
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            }
        });
    }

    // Logout button handling
    const logoutButtons = document.querySelectorAll('.logout-btn');
    logoutButtons.forEach(button => {
        button.addEventListener('click', async function(e) {
            e.preventDefault();

            // Disable the button to prevent multiple clicks
            if (this.classList.contains('disabled')) {
                return;
            }

            // Add visual feedback
            const originalText = this.innerHTML;
            this.classList.add('disabled');
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging out...';

            try {
                // Call the logout API using the API service
                const result = await apiService.logout();
                console.log('Logout result:', result);

                // Show success message
                if (result && result.success) {
                    // Optional: Show a success message
                    // alert('You have been successfully logged out.');
                }

                // Redirect to home page after logout
                window.location.href = '/';
            } catch (error) {
                console.error('Logout error:', error);

                // Even if there's an error, we should still clear local storage
                try {
                    localStorage.removeItem('token');
                    localStorage.removeItem('refreshToken');
                    localStorage.removeItem('user');
                    localStorage.removeItem('userFirstName');
                    localStorage.removeItem('userLastName');
                    console.log('Cleared local storage despite logout error');
                } catch (clearError) {
                    console.error('Error clearing local storage:', clearError);
                }

                alert('An error occurred during logout, but you have been logged out locally. Redirecting to home page...');

                // Still redirect to home page
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } finally {
                // Restore button state (though it will likely be removed from the page)
                this.classList.remove('disabled');
                this.innerHTML = originalText;
            }
        });
    });

    // Helper function to validate email
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Toggle password visibility
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const passwordField = this.previousElementSibling;
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            // Toggle icon
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    });

    // Check authentication status and update UI
    async function checkAuthStatus() {
        const token = localStorage.getItem('token');

        if (token) {
            try {
                // Get current user using the API service
                const result = await apiService.getCurrentUser();

                if (result.success) {
                    // User is logged in
                    updateUIForLoggedInUser(result.data);
                } else {
                    // Token is invalid
                    apiService.clearToken();
                    updateUIForLoggedOutUser();
                }
            } catch (error) {
                console.error('Auth status check error:', error);
                apiService.clearToken();
                updateUIForLoggedOutUser();
            }
        } else {
            // No token, user is not logged in
            updateUIForLoggedOutUser();
        }
    }

    // Update UI for logged in user
    function updateUIForLoggedInUser(user) {
        console.log('Updating UI for logged in user:', user);

        // Get user data from localStorage if not provided
        if (!user || !user.firstName) {
            const storedUser = JSON.parse(localStorage.getItem('user'));
            const firstName = localStorage.getItem('userFirstName');
            if (storedUser) {
                user = storedUser;
            } else if (firstName) {
                user = { firstName: firstName };
            } else {
                user = { firstName: 'User' };
            }
        }

        // Hide login/register buttons
        const loginButtons = document.querySelectorAll('.btn-login');
        const registerButtons = document.querySelectorAll('.btn-register');

        loginButtons.forEach(button => {
            button.style.display = 'none';
        });

        registerButtons.forEach(button => {
            button.style.display = 'none';
        });

        // Add user menu if it doesn't exist
        const navMenu = document.querySelector('.nav-menu');
        if (navMenu) {
            if (!document.querySelector('.user-menu')) {
                const userMenuItem = document.createElement('li');
                userMenuItem.className = 'user-menu-item';
                userMenuItem.innerHTML = `
                    <div class="user-menu">
                        <span class="user-greeting">Hello, <span id="nav-user-name" class="user-name">${user.firstName}</span>!</span>
                        <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                `;
                navMenu.appendChild(userMenuItem);

                // Add event listener to the new logout button
                const logoutBtn = userMenuItem.querySelector('.logout-btn');
                logoutBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    // Disable the button to prevent multiple clicks
                    if (this.classList.contains('disabled')) {
                        return;
                    }

                    // Add visual feedback
                    const originalText = this.innerHTML;
                    this.classList.add('disabled');
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging out...';

                    try {
                        // Call the logout API using the API service
                        const result = await apiService.logout();
                        console.log('Logout result:', result);

                        // The API service already clears all local storage items

                        // Redirect to home page after logout
                        window.location.href = '/';
                    } catch (error) {
                        console.error('Logout error:', error);

                        // Even if there's an error, we should still clear local storage
                        try {
                            localStorage.removeItem('token');
                            localStorage.removeItem('refreshToken');
                            localStorage.removeItem('user');
                            localStorage.removeItem('userFirstName');
                            localStorage.removeItem('userLastName');
                            console.log('Cleared local storage despite logout error');
                        } catch (clearError) {
                            console.error('Error clearing local storage:', clearError);
                        }

                        alert('An error occurred during logout, but you have been logged out locally. Redirecting to home page...');

                        // Still redirect to home page
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    } finally {
                        // Restore button state (though it will likely be removed from the page)
                        this.classList.remove('disabled');
                        this.innerHTML = originalText;
                    }
                });

                // Add styles to highlight the user menu
                const style = document.createElement('style');
                style.textContent = `
                    .user-menu {
                        display: flex;
                        align-items: center;
                        background-color: var(--primary-color-light);
                        padding: 8px 15px;
                        border-radius: 20px;
                        margin-left: 15px;
                    }
                    .user-greeting {
                        margin-right: 15px;
                        font-weight: 500;
                    }
                    .user-name {
                        font-weight: 600;
                        color: var(--primary-color);
                    }
                    .logout-btn {
                        color: var(--primary-color);
                        font-weight: 500;
                        display: flex;
                        align-items: center;
                        transition: color 0.3s;
                    }
                    .logout-btn:hover {
                        color: var(--accent-color);
                    }
                    .logout-btn i {
                        margin-right: 5px;
                    }
                    @media (max-width: 768px) {
                        .user-menu {
                            flex-direction: column;
                            align-items: flex-start;
                            padding: 10px;
                            margin-left: 0;
                        }
                        .user-greeting {
                            margin-right: 0;
                            margin-bottom: 8px;
                        }
                    }
                `;
                document.head.appendChild(style);
            } else {
                // Update all user name elements on the page
                // First, check for elements with class 'user-name'
                const userNameElements = document.querySelectorAll('.user-name');
                userNameElements.forEach(element => {
                    element.textContent = user.firstName;
                });

                // Also check for specific ID elements that might be used for user names
                const specificUserNameElements = [
                    document.getElementById('nav-user-name'),
                    document.getElementById('user-name'),
                    document.getElementById('sidebar-user-name')
                ];

                specificUserNameElements.forEach(element => {
                    if (element) {
                        element.textContent = user.firstName;
                    }
                });
            }
        }
    }

    // Update UI for logged out user
    function updateUIForLoggedOutUser() {
        console.log('Updating UI for logged out user');

        // Show login/register buttons
        const loginButtons = document.querySelectorAll('.btn-login');
        const registerButtons = document.querySelectorAll('.btn-register');

        loginButtons.forEach(button => {
            button.style.display = '';
        });

        registerButtons.forEach(button => {
            button.style.display = '';
        });

        // Remove user menu if it exists
        const userMenuItem = document.querySelector('.user-menu-item');
        if (userMenuItem) {
            userMenuItem.remove();
        } else {
            // Fallback to old method if user-menu-item class is not found
            const userMenu = document.querySelector('.user-menu');
            if (userMenu && userMenu.parentElement) {
                userMenu.parentElement.remove();
            }
        }
    }
});
