const Content = require('../models/Content');
const Activity = require('../models/Activity');

// Get all content with filtering, sorting, and pagination
exports.getAllContent = async (req, res, next) => {
  try {
    // Implement pagination, filtering, and sorting
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;

    // Build query
    let query = Content.find();

    // Filter by type
    if (req.query.type) {
      query = query.find({ type: req.query.type });
    }

    // Filter by status
    if (req.query.status) {
      query = query.find({ status: req.query.status });
    }

    // Filter by category
    if (req.query.category) {
      query = query.find({ category: req.query.category });
    }

    // Search by title or content
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      query = query.find({
        $or: [
          { title: searchRegex },
          { content: searchRegex },
          { summary: searchRegex },
        ],
      });
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // Populate author
    query = query.populate({
      path: 'author',
      select: 'firstName lastName',
    });

    // Execute query with pagination
    const content = await query.skip(skip).limit(limit);

    // Get total count for pagination
    const total = await Content.countDocuments(query.getFilter());

    res.status(200).json({
      success: true,
      count: content.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
      data: content,
    });
  } catch (error) {
    next(error);
  }
};

// Get content by ID
exports.getContentById = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id).populate({
      path: 'author',
      select: 'firstName lastName',
    });

    if (!content) {
      return res.status(404).json({
        success: false,
        message: 'Content not found',
      });
    }

    // Increment view count if not admin
    if (req.user.role !== 'admin') {
      content.views += 1;
      await content.save({ validateBeforeSave: false });
    }

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (error) {
    next(error);
  }
};

// Create new content (admin only)
exports.createContent = async (req, res, next) => {
  try {
    // Add author to request body
    req.body.author = req.user.id;

    // Set publishedAt date if status is published
    if (req.body.status === 'published') {
      req.body.publishedAt = Date.now();
    }

    const content = await Content.create(req.body);

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'content_create',
      details: `Content created: ${content.title}`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(201).json({
      success: true,
      data: content,
    });
  } catch (error) {
    next(error);
  }
};

// Update content (admin only)
exports.updateContent = async (req, res, next) => {
  try {
    // Set publishedAt date if status is being changed to published
    const content = await Content.findById(req.params.id);
    
    if (!content) {
      return res.status(404).json({
        success: false,
        message: 'Content not found',
      });
    }

    // Set publishedAt if status is changing to published
    if (req.body.status === 'published' && content.status !== 'published') {
      req.body.publishedAt = Date.now();
    }

    const updatedContent = await Content.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true,
      }
    ).populate({
      path: 'author',
      select: 'firstName lastName',
    });

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'content_update',
      details: `Content updated: ${updatedContent.title}`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(200).json({
      success: true,
      data: updatedContent,
    });
  } catch (error) {
    next(error);
  }
};

// Delete content (admin only)
exports.deleteContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        message: 'Content not found',
      });
    }

    await Content.findByIdAndDelete(req.params.id);

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'content_delete',
      details: `Content deleted: ${content.title}`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(200).json({
      success: true,
      message: 'Content deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};
