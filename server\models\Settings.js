const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  key: {
    type: String,
    required: [true, 'Setting key is required'],
    unique: true,
    trim: true,
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, 'Setting value is required'],
  },
  group: {
    type: String,
    enum: ['general', 'email', 'security', 'appearance', 'other'],
    default: 'other',
  },
  description: {
    type: String,
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Pre-save middleware to update the updatedAt field
settingsSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

// Index for faster queries
settingsSchema.index({ group: 1 });

const Settings = mongoose.model('Settings', settingsSchema);

module.exports = Settings;
