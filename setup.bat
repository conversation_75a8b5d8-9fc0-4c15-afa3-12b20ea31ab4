@echo off
echo ===================================================
echo MATERNIFY - Setup and Installation
echo ===================================================
echo.

echo Step 1: Installing server dependencies...
cd server
call npm install
cd ..

echo.
echo Step 2: Setting up the database...
call npm run seed

echo.
echo Step 3: Starting the server...
call npm run dev

echo.
echo If the server started successfully, you can access the application at:
echo http://localhost:8080
echo.
echo For the admin dashboard, use:
echo http://localhost:8080/admin-dashboard
echo.
echo Admin credentials:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo Press Ctrl+C to stop the server when you're done.
