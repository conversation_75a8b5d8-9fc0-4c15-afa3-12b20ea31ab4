const express = require('express');
const activityController = require('../controllers/activityController');
const { protect, restrictTo } = require('../middleware/auth');

const router = express.Router();

// Protect all routes and restrict to admin
router.use(protect);
router.use(restrictTo('admin'));

// Admin-only routes
router.get('/', activityController.getAllActivities);
router.get('/stats', activityController.getActivityStats);

module.exports = router;
