const mongoose = require('mongoose');
const dotenv = require('dotenv');
const fs = require('fs');

// Load env vars
dotenv.config();

// Function to test MongoDB connection
const testConnection = async () => {
  try {
    console.log('Testing MongoDB connection...');
    
    // Check if MONGO_URI is set
    if (!process.env.MONGO_URI) {
      throw new Error('MongoDB connection string is not defined in environment variables');
    }
    
    console.log(`Attempting to connect to: ${process.env.MONGO_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);
    
    // Connect to MongoDB
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    console.log('Connection state:', conn.connection.readyState);
    console.log('Database name:', conn.connection.name);
    
    // List collections
    const collections = await conn.connection.db.listCollections().toArray();
    console.log('\nCollections:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Close connection
    await mongoose.connection.close();
    console.log('\nConnection closed successfully');
    
    return true;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error.message);
    return false;
  }
};

// Run the test
testConnection()
  .then(success => {
    if (success) {
      console.log('\nMongoDB connection test passed!');
    } else {
      console.log('\nMongoDB connection test failed!');
    }
    process.exit(0);
  })
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
