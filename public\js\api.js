// API handling functions for MATERNIFY

// Helper function to get auth header
function getAuthHeader() {
    const token = localStorage.getItem('token');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
}

// Login function
async function loginUser(email, password) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Login error:', error);
        return { success: false, message: 'An error occurred during login. Please try again.' };
    }
}

// Register function
async function registerUser(userData) {
    try {
        console.log('registerUser called with:', { ...userData, password: '***' });

        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });

        console.log('Register API response status:', response.status);

        const data = await response.json();
        console.log('Register API response data:', data);

        return data;
    } catch (error) {
        console.error('Registration error:', error);
        return { success: false, message: 'An error occurred during registration. Please try again.' };
    }
}

// Logout function
async function logoutUser() {
    try {
        const response = await fetch('/api/logout', {
            method: 'GET',
            headers: {
                ...getAuthHeader()
            }
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Logout error:', error);
        return { success: false, message: 'An error occurred during logout. Please try again.' };
    }
}

// Get current user
async function getCurrentUser() {
    try {
        const response = await fetch('/api/me', {
            method: 'GET',
            headers: {
                ...getAuthHeader()
            }
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Get user error:', error);
        return { success: false, message: 'An error occurred while fetching user data.' };
    }
}

// Newsletter subscription
async function subscribeToNewsletter(email) {
    try {
        const response = await fetch('/api/subscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeader()
            },
            body: JSON.stringify({ email })
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Subscription error:', error);
        return { success: false, message: 'An error occurred during subscription. Please try again.' };
    }
}

// Due date calculator
async function calculateDueDate(lmpDate) {
    try {
        const response = await fetch('/api/calculate-due-date', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeader()
            },
            body: JSON.stringify({ lmpDate })
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Due date calculation error:', error);
        return { success: false, message: 'An error occurred during calculation. Please try again.' };
    }
}

// BMI calculator
async function calculateBMI(weight, height, unit) {
    try {
        const response = await fetch('/api/calculate-bmi', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...getAuthHeader()
            },
            body: JSON.stringify({ weight, height, unit })
        });

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('BMI calculation error:', error);
        return { success: false, message: 'An error occurred during calculation. Please try again.' };
    }
}
