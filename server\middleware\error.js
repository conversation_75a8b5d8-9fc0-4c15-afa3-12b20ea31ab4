const SystemLog = require('../models/SystemLog');

// Log error to database
const logErrorToDb = async (err, req) => {
  try {
    await SystemLog.create({
      level: 'error',
      message: err.message,
      source: req.originalUrl,
      stack: err.stack,
    });
  } catch (logError) {
    console.error('Error logging to database:', logError);
  }
};

// Error handler middleware
const errorHandler = async (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  
  // Log error to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('ERROR 💥', err);
  }
  
  // Log error to database
  await logErrorToDb(err, req);

  // Handle different types of errors
  let error = { ...err };
  error.message = err.message;

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    error.message = Object.values(err.errors)
      .map(val => val.message)
      .join(', ');
    error.statusCode = 400;
  }

  // Mongoose duplicate key error
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    error.message = `Duplicate field value: ${field}. Please use another value.`;
    error.statusCode = 400;
  }

  // Mongoose cast error
  if (err.name === 'CastError') {
    error.message = `Invalid ${err.path}: ${err.value}`;
    error.statusCode = 400;
  }

  // JWT error
  if (err.name === 'JsonWebTokenError') {
    error.message = 'Invalid token. Please log in again.';
    error.statusCode = 401;
  }

  // JWT expired error
  if (err.name === 'TokenExpiredError') {
    error.message = 'Your token has expired. Please log in again.';
    error.statusCode = 401;
  }

  // Send response
  res.status(error.statusCode).json({
    success: false,
    message: error.message,
    error: process.env.NODE_ENV === 'development' ? error : undefined,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
  });
};

module.exports = errorHandler;
