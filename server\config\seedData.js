const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('../models/User');
const Settings = require('../models/Settings');
const Content = require('../models/Content');
const SystemLog = require('../models/SystemLog');

// Load environment variables
dotenv.config();

// Connect to database
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Create default admin user
const createAdminUser = async () => {
  try {
    // Check if admin user already exists
    const adminExists = await User.findOne({ email: '<EMAIL>' });

    if (adminExists) {
      console.log('Admin user already exists');
      return;
    }

    // Create admin user
    await User.create({
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
    });

    console.log('Admin user created');
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
};

// Create default settings
const createDefaultSettings = async () => {
  try {
    // Default settings
    const defaultSettings = [
      {
        key: 'siteName',
        value: 'MATERNIFY',
        group: 'general',
        description: 'Site name',
      },
      {
        key: 'siteDescription',
        value: 'Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.',
        group: 'general',
        description: 'Site description',
      },
      {
        key: 'adminEmail',
        value: '<EMAIL>',
        group: 'email',
        description: 'Admin email address',
      },
      {
        key: 'supportEmail',
        value: '<EMAIL>',
        group: 'email',
        description: 'Support email address',
      },
      {
        key: 'enableTwoFactor',
        value: true,
        group: 'security',
        description: 'Enable two-factor authentication for admins',
      },
      {
        key: 'enableCaptcha',
        value: true,
        group: 'security',
        description: 'Enable CAPTCHA on registration',
      },
      {
        key: 'maxLoginAttempts',
        value: 5,
        group: 'security',
        description: 'Maximum login attempts before account lockout',
      },
    ];

    // Insert settings if they don't exist
    for (const setting of defaultSettings) {
      const exists = await Settings.findOne({ key: setting.key });
      if (!exists) {
        await Settings.create(setting);
        console.log(`Setting created: ${setting.key}`);
      }
    }

    console.log('Default settings created');
  } catch (error) {
    console.error('Error creating default settings:', error);
  }
};

// Create sample content
const createSampleContent = async () => {
  try {
    // Check if content already exists
    const contentExists = await Content.countDocuments();

    if (contentExists > 0) {
      console.log('Sample content already exists');
      return;
    }

    // Get admin user
    const admin = await User.findOne({ role: 'admin' });

    if (!admin) {
      console.log('Admin user not found');
      return;
    }

    // Sample content
    const sampleContent = [
      {
        title: 'Pregnancy Care Guidelines',
        slug: 'pregnancy-care-guidelines',
        type: 'page',
        content: '<h1>Pregnancy Care Guidelines</h1><p>This is a sample page about pregnancy care guidelines.</p>',
        summary: 'Guidelines for pregnancy care',
        category: 'pregnancy',
        author: admin._id,
        status: 'published',
        publishedAt: new Date(),
      },
      {
        title: 'Infant Care Guide',
        slug: 'infant-care-guide',
        type: 'page',
        content: '<h1>Infant Care Guide</h1><p>This is a sample page about infant care.</p>',
        summary: 'Guide for infant care',
        category: 'infant-care',
        author: admin._id,
        status: 'published',
        publishedAt: new Date(),
      },
      {
        title: 'Nutrition During Pregnancy',
        slug: 'nutrition-during-pregnancy',
        type: 'article',
        content: '<h1>Nutrition During Pregnancy</h1><p>This is a sample article about nutrition during pregnancy.</p>',
        summary: 'Important nutrition tips for pregnant women',
        category: 'nutrition',
        author: admin._id,
        status: 'published',
        publishedAt: new Date(),
      },
      {
        title: 'Baby Sleep Patterns',
        slug: 'baby-sleep-patterns',
        type: 'article',
        content: '<h1>Baby Sleep Patterns</h1><p>This is a sample article about baby sleep patterns.</p>',
        summary: 'Understanding baby sleep patterns',
        category: 'infant-care',
        author: admin._id,
        status: 'published',
        publishedAt: new Date(),
      },
      {
        title: 'Breastfeeding Techniques',
        slug: 'breastfeeding-techniques',
        type: 'video',
        content: '<h1>Breastfeeding Techniques</h1><p>This is a sample video about breastfeeding techniques.</p><p>Video URL: https://example.com/video</p>',
        summary: 'Learn proper breastfeeding techniques',
        category: 'breastfeeding',
        author: admin._id,
        status: 'published',
        publishedAt: new Date(),
      },
    ];

    // Create sample content
    await Content.insertMany(sampleContent);

    console.log('Sample content created');
  } catch (error) {
    console.error('Error creating sample content:', error);
  }
};

// Create sample system logs
const createSampleLogs = async () => {
  try {
    // Check if logs already exist
    const logsExist = await SystemLog.countDocuments();

    if (logsExist > 0) {
      console.log('Sample logs already exist');
      return;
    }

    // Sample logs
    const sampleLogs = [
      {
        level: 'info',
        message: 'System initialized',
        source: 'system',
        timestamp: new Date(),
      },
      {
        level: 'info',
        message: 'Database connected',
        source: 'database',
        timestamp: new Date(),
      },
      {
        level: 'warning',
        message: 'Database connection pool reaching capacity (80%)',
        source: 'database',
        timestamp: new Date(),
      },
      {
        level: 'error',
        message: 'Failed to send email notification',
        source: 'email',
        stack: 'Error: Failed to send email\n    at sendEmail (email.js:25)\n    at notifyUser (notification.js:10)',
        timestamp: new Date(),
      },
    ];

    // Create sample logs
    await SystemLog.insertMany(sampleLogs);

    console.log('Sample logs created');
  } catch (error) {
    console.error('Error creating sample logs:', error);
  }
};

// Run seed functions
const seedData = async () => {
  try {
    await createAdminUser();
    await createDefaultSettings();
    await createSampleContent();
    await createSampleLogs();

    console.log('Seed data created successfully');
    process.exit();
  } catch (error) {
    console.error('Error seeding data:', error);
    process.exit(1);
  }
};

seedData();
