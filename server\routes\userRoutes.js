const express = require('express');
const userController = require('../controllers/userController');
const { protect, restrictTo } = require('../middleware/auth');

const router = express.Router();

// Protect all routes
router.use(protect);

// Admin-only routes
router.get('/', restrictTo('admin'), userController.getAllUsers);
router.post('/', restrictTo('admin'), userController.createUser);

// Routes for both admin and user (with restrictions in controller)
router.get('/:id', userController.getUserById);
router.patch('/:id', userController.updateUser);
router.delete('/:id', restrictTo('admin'), userController.deleteUser);

module.exports = router;
