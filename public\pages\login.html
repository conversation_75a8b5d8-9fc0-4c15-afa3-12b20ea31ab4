<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login | MATERNIFY</title>
    <meta name="description" content="Log in to your MATERNIFY account to access personalized pregnancy and infant care tools and community features.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Custom styles for header to match footer -->
    <style>
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .nav-menu li a.btn-login,
        header .nav-menu li a.btn-register {
            color: var(--text-color) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }
    </style>
    <style>
        /* CSS Variables for consistent colors */
        :root {
            --primary-rgb: 92, 107, 192;
            --input-bg: #f8f8f8;
            --input-border: #ccc;
            --input-text: #333;
            --icon-color: #333;  /* Black color for icons */
            --checkbox-size: 18px;
        }

        /* Custom checkbox styling */
        .checkbox-group {
            display: flex;
            align-items: center;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            cursor: pointer;
            width: var(--checkbox-size);
            height: var(--checkbox-size);
            border: 1px solid var(--input-border);
            border-radius: 3px;
            accent-color: var(--primary-color);
        }

        .checkbox-group label {
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 0.95rem;
            color: var(--input-text);
            font-weight: 500;
        }

        .checkbox-group label i {
            margin-right: 6px;
            color: var(--icon-color);
            font-size: 1rem;
        }

        /* Form footer styling */
        .form-footer {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .form-footer .checkbox-options {
            display: flex;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .forgot-password i {
            margin-right: 6px;
            color: var(--icon-color);
        }

        /* Form field icons and labels */
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--input-text);
            font-size: 0.95rem;
        }

        .form-group label i {
            margin-right: 6px;
            color: var(--icon-color);
        }

        /* Input field styling to match registration page */
        .form-group input[type="email"],
        .form-group input[type="password"] {
            border: 1px solid var(--input-border);
            background-color: var(--input-bg);
            color: var(--input-text);
            font-weight: 500;
            padding: 12px 15px;
            border-radius: 5px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input[type="email"]:focus,
        .form-group input[type="password"]:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
            outline: none;
        }

        /* Button styling to match registration page */
        .btn-submit {
            background-color: var(--primary-color);
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-submit i {
            font-size: 1.1rem;
            color: white; /* Keep the icon white for contrast on the colored button */
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li><a href="/login" class="btn-login active">Login</a></li>
                    <li><a href="/register" class="btn-register">Register</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-section" style="padding: var(--spacing-xxl) 0; background-color: var(--light-bg);">
        <div class="container">
            <div class="auth-container">
                <div class="auth-header">
                    <h1>Welcome Back</h1>
                    <p>Log in to access your personalized tools and community</p>
                    <div id="registration-message" style="display: none; margin-top: 15px; padding: 10px; background-color: #d4edda; color: #155724; border-radius: 5px; text-align: center;">
                        <i class="fas fa-check-circle"></i> Registration successful! Please log in with your new account.
                    </div>
                    <div id="social-registration-message" style="display: none; margin-top: 15px; padding: 10px; background-color: #d4edda; color: #155724; border-radius: 5px; text-align: center;">
                        <i class="fas fa-check-circle"></i> Social account connected! Please log in to continue.
                    </div>
                </div>

                <form class="auth-form" id="loginForm">
                    <div class="form-group">
                        <label for="email"><i class="fas fa-envelope"></i> Email Address</label>
                        <input type="email" id="email" name="email" required>
                        <div class="error-message" id="emailError" style="display: none;">Please enter a valid email address</div>
                    </div>

                    <div class="form-group">
                        <label for="password"><i class="fas fa-lock"></i> Password</label>
                        <div class="password-field">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                <i class="far fa-eye"></i>
                            </button>
                        </div>
                        <div class="error-message" id="passwordError" style="display: none;">Please enter your password</div>
                    </div>

                    <div class="form-footer">
                        <div class="checkbox-options">
                            <div class="checkbox-group">
                                <input type="checkbox" id="rememberMe" name="rememberMe">
                                <label for="rememberMe"><i class="fas fa-user-clock"></i> Remember me</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="checkbox" id="stayLoggedIn" name="stayLoggedIn">
                                <label for="stayLoggedIn"><i class="fas fa-shield-alt"></i> Stay logged in</label>
                            </div>
                        </div>
                        <a href="/forgot-password" class="forgot-password"><i class="fas fa-key"></i> Forgot password?</a>
                    </div>

                    <button type="submit" class="btn-submit"><i class="fas fa-sign-in-alt"></i> Log In</button>
                </form>

                <div class="auth-divider">
                    <span>or log in with</span>
                </div>

                <div class="social-auth">
                    <button class="social-auth-btn google">
                        <i class="fab fa-google"></i> Google
                    </button>
                    <button class="social-auth-btn facebook">
                        <i class="fab fa-facebook-f"></i> Facebook
                    </button>
                </div>

                <div class="auth-switch">
                    Don't have an account? <a href="/register">Sign Up</a>
                </div>

                <!-- Admin login info - for demo purposes -->
                <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; font-size: 0.9rem; text-align: center;">
                    <p><strong>Admin Demo:</strong> Use <code><EMAIL></code> / <code>admin123</code> to access admin features</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                        <li><a href="/infant-care">Infant Care</a></li>
                        <li><a href="/tools">Tools</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/blog">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/tools#due-date">Due Date Calculator</a></li>
                        <li><a href="/tools#bmi">BMI Tracker</a></li>
                        <li><a href="/tools#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/tools#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user was redirected from registration page
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('registered') && urlParams.get('registered') === 'true') {
                // Check if it's a social registration
                if (urlParams.has('social')) {
                    // Show social registration success message
                    const socialRegistrationMessage = document.getElementById('social-registration-message');
                    if (socialRegistrationMessage) {
                        socialRegistrationMessage.style.display = 'block';

                        // Update message based on social platform
                        const socialPlatform = urlParams.get('social');
                        if (socialPlatform === 'google' || socialPlatform === 'facebook') {
                            socialRegistrationMessage.innerHTML = `<i class="fas fa-check-circle"></i> ${socialPlatform.charAt(0).toUpperCase() + socialPlatform.slice(1)} account connected! Please log in to continue.`;
                        }

                        // Scroll to the message
                        socialRegistrationMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                } else {
                    // Show regular registration success message
                    const registrationMessage = document.getElementById('registration-message');
                    if (registrationMessage) {
                        registrationMessage.style.display = 'block';

                        // Auto-fill email if provided
                        if (urlParams.has('email')) {
                            const emailField = document.getElementById('email');
                            if (emailField) {
                                emailField.value = urlParams.get('email');
                            }
                        }

                        // Scroll to the message
                        registrationMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }

                // Remove the query parameters from the URL without refreshing the page
                window.history.replaceState({}, document.title, window.location.pathname);
            }

            // Handle checkbox interactions
            const rememberMeCheckbox = document.getElementById('rememberMe');
            const stayLoggedInCheckbox = document.getElementById('stayLoggedIn');

            // If "Stay logged in" is checked, "Remember me" should also be checked
            stayLoggedInCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    rememberMeCheckbox.checked = true;
                }
            });

            // If "Remember me" is unchecked, "Stay logged in" should also be unchecked
            rememberMeCheckbox.addEventListener('change', function() {
                if (!this.checked) {
                    stayLoggedInCheckbox.checked = false;
                }
            });

            // Handle form submission
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    // Get form values
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;
                    const rememberMe = document.getElementById('rememberMe').checked;
                    const stayLoggedIn = document.getElementById('stayLoggedIn').checked;

                    // Validate form
                    let isValid = true;

                    // Email validation
                    if (!email || !isValidEmail(email)) {
                        document.getElementById('emailError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('emailError').style.display = 'none';
                    }

                    // Password validation
                    if (!password) {
                        document.getElementById('passwordError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('passwordError').style.display = 'none';
                    }

                    if (!isValid) {
                        return;
                    }

                    // Show loading state
                    const submitButton = loginForm.querySelector('.btn-submit');
                    const originalText = submitButton.innerHTML;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
                    submitButton.disabled = true;

                    try {
                        console.log('Login form submitted with email:', email);

                        // For testing/demo purposes, use a hardcoded login for any email/password
                        // This simulates the server's fallback mode
                        if (email && password) {
                            // Create a mock token
                            const mockToken = 'mock_token_' + Date.now();
                            localStorage.setItem('token', mockToken);

                            // Create a mock user - make it an admin if using admin credentials
                            let mockUser;

                            if (email.toLowerCase() === '<EMAIL>' && password === 'admin123') {
                                // Admin user
                                mockUser = {
                                    firstName: 'Admin',
                                    lastName: 'User',
                                    email: email,
                                    role: 'admin'
                                };
                                console.log('Admin login detected');
                            } else {
                                // Regular user
                                mockUser = {
                                    firstName: 'Test',
                                    lastName: 'User',
                                    email: email
                                };
                            }

                            // Store user data
                            localStorage.setItem('user', JSON.stringify(mockUser));
                            localStorage.setItem('userFirstName', mockUser.firstName);
                            localStorage.setItem('userLastName', mockUser.lastName);

                            console.log('Mock login successful, redirecting to dashboard...');

                            // Redirect to dashboard page without .html extension
                            window.location.href = '/dashboard';
                            return;
                        }

                        // If we get here, try the actual API
                        const result = await apiService.login(email, password, {
                            rememberMe,
                            stayLoggedIn
                        });

                        if (result.success) {
                            // Immediately redirect to dashboard page without .html extension
                            console.log('Login successful, redirecting to dashboard...');
                            window.location.href = '/dashboard';
                        } else {
                            // Show error message
                            console.error('Login failed:', result.message);
                            alert(result.message || 'Login failed. Please check your credentials and try again.');

                            // Reset button
                            submitButton.innerHTML = originalText;
                            submitButton.disabled = false;
                        }
                    } catch (error) {
                        console.error('Login error:', error);
                        alert('An error occurred during login. Please try again.');

                        // Reset button
                        submitButton.innerHTML = originalText;
                        submitButton.disabled = false;
                    }
                });
            }

            // Toggle password visibility
            const toggleButtons = document.querySelectorAll('.toggle-password');
            toggleButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const passwordField = this.previousElementSibling;
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);

                    // Toggle icon
                    const icon = this.querySelector('i');
                    icon.classList.toggle('fa-eye');
                    icon.classList.toggle('fa-eye-slash');
                });
            });

            // Helper function to validate email
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            // Social login buttons functionality
            const googleLoginBtn = document.querySelector('.social-auth-btn.google');
            const facebookLoginBtn = document.querySelector('.social-auth-btn.facebook');

            if (googleLoginBtn) {
                googleLoginBtn.addEventListener('click', function() {
                    // Create status message for social login
                    let socialStatus = document.createElement('div');
                    socialStatus.className = 'status-message';
                    socialStatus.style.padding = '10px';
                    socialStatus.style.marginTop = '15px';
                    socialStatus.style.borderRadius = '5px';
                    socialStatus.style.textAlign = 'center';
                    socialStatus.style.backgroundColor = '#f8f9fa';
                    socialStatus.style.color = '#333';
                    socialStatus.textContent = 'Connecting to Google...';

                    // Add to the page after the social auth buttons
                    document.querySelector('.social-auth').after(socialStatus);

                    // Disable buttons during "processing"
                    googleLoginBtn.disabled = true;
                    if (facebookLoginBtn) facebookLoginBtn.disabled = true;

                    // Simulate API call delay
                    setTimeout(function() {
                        socialStatus.style.backgroundColor = '#d4edda';
                        socialStatus.style.color = '#155724';
                        socialStatus.textContent = 'Google authentication successful! Redirecting to dashboard...';

                        // Create a mock token and user for Google login
                        const mockToken = 'google_mock_token_' + Date.now();
                        localStorage.setItem('token', mockToken);

                        // Create a mock user with Google info
                        const mockUser = {
                            firstName: 'Google',
                            lastName: 'User',
                            email: '<EMAIL>'
                        };

                        // Store user data
                        localStorage.setItem('user', JSON.stringify(mockUser));
                        localStorage.setItem('userFirstName', mockUser.firstName);
                        localStorage.setItem('userLastName', mockUser.lastName);

                        // Redirect to dashboard page without .html extension after a short delay
                        setTimeout(function() {
                            window.location.href = '/dashboard';
                        }, 1500);
                    }, 2000);
                });
            }

            if (facebookLoginBtn) {
                facebookLoginBtn.addEventListener('click', function() {
                    // Create status message for social login
                    let socialStatus = document.createElement('div');
                    socialStatus.className = 'status-message';
                    socialStatus.style.padding = '10px';
                    socialStatus.style.marginTop = '15px';
                    socialStatus.style.borderRadius = '5px';
                    socialStatus.style.textAlign = 'center';
                    socialStatus.style.backgroundColor = '#f8f9fa';
                    socialStatus.style.color = '#333';
                    socialStatus.textContent = 'Connecting to Facebook...';

                    // Add to the page after the social auth buttons
                    document.querySelector('.social-auth').after(socialStatus);

                    // Disable buttons during "processing"
                    facebookLoginBtn.disabled = true;
                    if (googleLoginBtn) googleLoginBtn.disabled = true;

                    // Simulate API call delay
                    setTimeout(function() {
                        socialStatus.style.backgroundColor = '#d4edda';
                        socialStatus.style.color = '#155724';
                        socialStatus.textContent = 'Facebook authentication successful! Redirecting to dashboard...';

                        // Create a mock token and user for Facebook login
                        const mockToken = 'facebook_mock_token_' + Date.now();
                        localStorage.setItem('token', mockToken);

                        // Create a mock user with Facebook info
                        const mockUser = {
                            firstName: 'Facebook',
                            lastName: 'User',
                            email: '<EMAIL>'
                        };

                        // Store user data
                        localStorage.setItem('user', JSON.stringify(mockUser));
                        localStorage.setItem('userFirstName', mockUser.firstName);
                        localStorage.setItem('userLastName', mockUser.lastName);

                        // Redirect to dashboard page without .html extension after a short delay
                        setTimeout(function() {
                            window.location.href = '/dashboard';
                        }, 1500);
                    }, 2000);
                });
            }
        });
    </script>
</body>
</html>
