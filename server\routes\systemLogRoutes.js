const express = require('express');
const systemLogController = require('../controllers/systemLogController');
const { protect, restrictTo } = require('../middleware/auth');

const router = express.Router();

// Protect all routes and restrict to admin
router.use(protect);
router.use(restrictTo('admin'));

// Admin-only routes
router.get('/', systemLogController.getAllLogs);
router.get('/stats', systemLogController.getLogStats);

module.exports = router;
