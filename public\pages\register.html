<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Account | MATERNIFY</title>
    <meta name="description" content="Create your MATERNIFY account to track your pregnancy journey, save baby growth data, and join our supportive community.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Custom styles for header to match footer -->
    <style>
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .nav-menu li a.btn-login,
        header .nav-menu li a.btn-register {
            color: var(--text-color) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }
    </style>
    <style>
        /* Custom checkbox styling */
        .checkbox-group {
            display: flex;
            align-items: center;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
            cursor: pointer;
            width: 18px;
            height: 18px;
        }

        .checkbox-group label {
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 0.95rem;
        }

        .checkbox-group label i {
            margin-right: 6px;
            color: var(--primary-color);
        }

        /* Tooltip styling */
        [title] {
            position: relative;
            cursor: help;
        }

        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            z-index: 10;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li><a href="/login" class="btn-login">Login</a></li>
                    <li><a href="/register" class="btn-register active">Register</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-section" style="padding: var(--spacing-xxl) 0; background-color: var(--light-bg);">
        <div class="container">
            <div class="auth-container" style="max-width: 550px;">
                <div class="auth-header">
                    <h1>Create Your Account</h1>
                    <p>Join our community of parents and access personalized tools</p>
                </div>

                <form class="auth-form" id="registerForm">
                    <div class="form-group" style="display: flex; gap: var(--spacing-md);">
                        <div style="flex: 1;">
                            <label for="firstName"><i class="fas fa-user"></i> First Name</label>
                            <input type="text" id="firstName" name="firstName" required>
                            <div class="error-message" id="firstNameError" style="display: none;">Please enter your first name</div>
                        </div>
                        <div style="flex: 1;">
                            <label for="lastName"><i class="fas fa-user"></i> Last Name</label>
                            <input type="text" id="lastName" name="lastName" required>
                            <div class="error-message" id="lastNameError" style="display: none;">Please enter your last name</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email"><i class="fas fa-envelope"></i> Email Address</label>
                        <input type="email" id="email" name="email" required>
                        <div class="error-message" id="emailError" style="display: none;">Please enter a valid email address</div>
                    </div>

                    <div class="form-group">
                        <label for="phone"><i class="fas fa-phone"></i> Phone Number</label>
                        <input type="tel" id="phone" name="phone" pattern="[0-9]{10}" placeholder="10-digit phone number" required>
                        <div class="error-message" id="phoneError" style="display: none;">Please enter a valid 10-digit phone number</div>
                    </div>

                    <div class="form-group" style="display: flex; gap: var(--spacing-md);">
                        <div style="flex: 1;">
                            <label for="dueDate"><i class="fas fa-calendar-alt"></i> Due Date (Optional)</label>
                            <input type="date" id="dueDate" name="dueDate">
                            <div class="helper-text" style="font-size: 0.8rem; color: var(--text-light); margin-top: 4px;">For pregnancy-related content</div>
                        </div>
                        <div style="flex: 1;">
                            <label for="babyBirthdate"><i class="fas fa-baby"></i> Baby's Birthdate (Optional)</label>
                            <input type="date" id="babyBirthdate" name="babyBirthdate">
                            <div class="helper-text" style="font-size: 0.8rem; color: var(--text-light); margin-top: 4px;">For infant care content</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password"><i class="fas fa-lock"></i> Password</label>
                        <div class="password-field">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                <i class="far fa-eye"></i>
                            </button>
                        </div>
                        <div class="error-message" id="passwordError" style="display: none;">Password must be at least 8 characters with letters and numbers</div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword"><i class="fas fa-lock"></i> Confirm Password</label>
                        <div class="password-field">
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                            <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                <i class="far fa-eye"></i>
                            </button>
                        </div>
                        <div class="error-message" id="confirmPasswordError" style="display: none;">Passwords do not match</div>
                    </div>

                    <div style="margin-bottom: var(--spacing-lg);">
                        <div class="checkbox-group" style="margin-bottom: var(--spacing-md);">
                            <input type="checkbox" id="termsAgree" name="termsAgree" required>
                            <label for="termsAgree"><i class="fas fa-check-circle"></i> I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></label>
                        </div>

                        <div style="display: flex; flex-wrap: wrap; gap: var(--spacing-md);">
                            <div class="checkbox-group" style="flex: 1; min-width: 200px;">
                                <input type="checkbox" id="newsletter" name="newsletter">
                                <label for="newsletter" title="Receive parenting tips and updates via email"><i class="fas fa-envelope-open-text"></i> Subscribe to newsletter</label>
                            </div>

                            <div class="checkbox-group" style="flex: 1; min-width: 200px;">
                                <input type="checkbox" id="smsUpdates" name="smsUpdates">
                                <label for="smsUpdates" title="Receive pregnancy and infant care milestone notifications via SMS"><i class="fas fa-sms"></i> Receive SMS updates</label>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn-submit"><i class="fas fa-user-plus"></i> Create Account</button>
                </form>

                <div class="auth-divider">
                    <span>or sign up with</span>
                </div>

                <div class="social-auth">
                    <button type="button" class="social-auth-btn google" id="googleSignup">
                        <i class="fab fa-google"></i> Google
                    </button>
                    <button type="button" class="social-auth-btn facebook" id="facebookSignup">
                        <i class="fab fa-facebook-f"></i> Facebook
                    </button>
                </div>

                <div class="auth-switch">
                    Already have an account? <a href="/login">Log In</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                        <li><a href="/infant-care">Infant Care</a></li>
                        <li><a href="/tools">Tools</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/blog">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/tools#due-date">Due Date Calculator</a></li>
                        <li><a href="/tools#bmi">BMI Tracker</a></li>
                        <li><a href="/tools#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/tools#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>

    <script>
        // Add a direct event listener to the form
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                console.log('Adding direct event listener to register form');

                // Add phone number validation
                const phoneInput = document.getElementById('phone');
                const phoneError = document.getElementById('phoneError');

                phoneInput.addEventListener('input', function() {
                    // Remove any non-numeric characters
                    this.value = this.value.replace(/\D/g, '');

                    // Check if valid
                    if (this.value.length === 10) {
                        phoneError.style.display = 'none';
                    } else {
                        phoneError.style.display = 'block';
                    }
                });

                registerForm.addEventListener('submit', async function(e) {
                    console.log('Direct event listener: Form submitted');
                    e.preventDefault();

                    // Get form values
                    const firstName = document.getElementById('firstName').value.trim();
                    const lastName = document.getElementById('lastName').value.trim();
                    const email = document.getElementById('email').value.trim();
                    const phone = document.getElementById('phone').value.trim();
                    const dueDate = document.getElementById('dueDate').value;
                    const babyBirthdate = document.getElementById('babyBirthdate').value;
                    const password = document.getElementById('password').value;
                    const confirmPassword = document.getElementById('confirmPassword').value;
                    const termsAgree = document.getElementById('termsAgree').checked;
                    const newsletter = document.getElementById('newsletter').checked;
                    const smsUpdates = document.getElementById('smsUpdates').checked;

                    // Validate form
                    let isValid = true;

                    // First name validation
                    if (!firstName) {
                        document.getElementById('firstNameError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('firstNameError').style.display = 'none';
                    }

                    // Last name validation
                    if (!lastName) {
                        document.getElementById('lastNameError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('lastNameError').style.display = 'none';
                    }

                    // Email validation
                    if (!email || !isValidEmail(email)) {
                        document.getElementById('emailError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('emailError').style.display = 'none';
                    }

                    // Phone validation
                    if (!phone || phone.length !== 10) {
                        document.getElementById('phoneError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('phoneError').style.display = 'none';
                    }

                    // Password validation
                    if (!password || password.length < 8 || !hasLettersAndNumbers(password)) {
                        document.getElementById('passwordError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('passwordError').style.display = 'none';
                    }

                    // Confirm password validation
                    if (password !== confirmPassword) {
                        document.getElementById('confirmPasswordError').style.display = 'block';
                        isValid = false;
                    } else {
                        document.getElementById('confirmPasswordError').style.display = 'none';
                    }

                    // Terms and conditions validation
                    if (!termsAgree) {
                        // Add error message for terms if not already present
                        let termsError = document.getElementById('termsError');
                        if (!termsError) {
                            termsError = document.createElement('div');
                            termsError.id = 'termsError';
                            termsError.className = 'error-message';
                            termsError.textContent = 'You must agree to the Terms of Service and Privacy Policy';
                            document.getElementById('termsAgree').parentNode.appendChild(termsError);
                        }
                        termsError.style.display = 'block';
                        isValid = false;
                    } else {
                        const termsError = document.getElementById('termsError');
                        if (termsError) {
                            termsError.style.display = 'none';
                        }
                    }

                    // Date validations
                    if (dueDate && babyBirthdate) {
                        // Show warning if both dates are provided
                        alert('Note: You have provided both a due date and a baby birthdate. We will use both to personalize your experience.');
                    }

                    if (!isValid) {
                        return;
                    }

                    try {
                        // Show loading state
                        const submitButton = registerForm.querySelector('.btn-submit');
                        const originalText = submitButton.innerHTML;
                        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Account...';
                        submitButton.disabled = true;

                        // Create a status message element
                        let statusMessage = document.getElementById('registrationStatus');
                        if (!statusMessage) {
                            statusMessage = document.createElement('div');
                            statusMessage.id = 'registrationStatus';
                            statusMessage.className = 'status-message';
                            statusMessage.style.padding = '10px';
                            statusMessage.style.marginTop = '15px';
                            statusMessage.style.borderRadius = '5px';
                            statusMessage.style.textAlign = 'center';
                            registerForm.appendChild(statusMessage);
                        }

                        statusMessage.textContent = 'Creating your account...';
                        statusMessage.style.backgroundColor = '#f8f9fa';
                        statusMessage.style.color = '#333';
                        statusMessage.style.display = 'block';

                        // Call the API using the API service
                        const data = await apiService.register({
                            firstName,
                            lastName,
                            email,
                            phone,
                            dueDate: dueDate || null,
                            babyBirthdate: babyBirthdate || null,
                            password,
                            preferences: {
                                newsletter,
                                smsUpdates
                            }
                        });

                        if (data.success) {
                            // Show success message
                            statusMessage.textContent = 'Registration successful! Redirecting to login page...';
                            statusMessage.style.backgroundColor = '#d4edda';
                            statusMessage.style.color = '#155724';

                            // Disable form inputs
                            const formInputs = registerForm.querySelectorAll('input, button');
                            formInputs.forEach(input => {
                                input.disabled = true;
                            });

                            // Redirect to login page after a short delay
                            setTimeout(() => {
                                window.location.href = `/login?registered=true&email=${encodeURIComponent(email)}`;
                            }, 2000);
                        } else {
                            // Show error message
                            statusMessage.textContent = data.message || 'Registration failed. Please try again.';
                            statusMessage.style.backgroundColor = '#f8d7da';
                            statusMessage.style.color = '#721c24';

                            // Reset button
                            submitButton.innerHTML = originalText;
                            submitButton.disabled = false;

                            // Scroll to the error message
                            statusMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    } catch (error) {
                        console.error('Registration error:', error);

                        // Show error message
                        let statusMessage = document.getElementById('registrationStatus');
                        if (statusMessage) {
                            statusMessage.textContent = 'An error occurred during registration. Please try again.';
                            statusMessage.style.backgroundColor = '#f8d7da';
                            statusMessage.style.color = '#721c24';
                            statusMessage.style.display = 'block';
                        }

                        // Reset button
                        const submitButton = registerForm.querySelector('.btn-submit');
                        submitButton.innerHTML = '<i class="fas fa-user-plus"></i> Create Account';
                        submitButton.disabled = false;
                    }
                });

                // Add real-time validation for password
                const passwordInput = document.getElementById('password');
                const confirmPasswordInput = document.getElementById('confirmPassword');

                // Toggle password visibility
                const togglePasswordButtons = document.querySelectorAll('.toggle-password');
                togglePasswordButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const input = this.parentElement.querySelector('input');
                        const icon = this.querySelector('i');

                        if (input.type === 'password') {
                            input.type = 'text';
                            icon.classList.remove('fa-eye');
                            icon.classList.add('fa-eye-slash');
                        } else {
                            input.type = 'password';
                            icon.classList.remove('fa-eye-slash');
                            icon.classList.add('fa-eye');
                        }
                    });
                });

                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    const passwordError = document.getElementById('passwordError');

                    if (password.length < 8 || !hasLettersAndNumbers(password)) {
                        passwordError.style.display = 'block';
                    } else {
                        passwordError.style.display = 'none';
                    }

                    // Check confirm password match if it has a value
                    if (confirmPasswordInput.value) {
                        const confirmPasswordError = document.getElementById('confirmPasswordError');
                        if (password !== confirmPasswordInput.value) {
                            confirmPasswordError.style.display = 'block';
                        } else {
                            confirmPasswordError.style.display = 'none';
                        }
                    }
                });

                confirmPasswordInput.addEventListener('input', function() {
                    const confirmPassword = this.value;
                    const password = passwordInput.value;
                    const confirmPasswordError = document.getElementById('confirmPasswordError');

                    if (password !== confirmPassword) {
                        confirmPasswordError.style.display = 'block';
                    } else {
                        confirmPasswordError.style.display = 'none';
                    }
                });
            }

            // Helper function to validate email
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            // Helper function to check if password has both letters and numbers
            function hasLettersAndNumbers(password) {
                return /[A-Za-z]/.test(password) && /\d/.test(password);
            }

            // Social login buttons
            const googleSignupBtn = document.getElementById('googleSignup');
            const facebookSignupBtn = document.getElementById('facebookSignup');

            if (googleSignupBtn) {
                googleSignupBtn.addEventListener('click', function() {
                    // Create status message for social login
                    let socialStatus = document.createElement('div');
                    socialStatus.className = 'status-message';
                    socialStatus.style.padding = '10px';
                    socialStatus.style.marginTop = '15px';
                    socialStatus.style.borderRadius = '5px';
                    socialStatus.style.textAlign = 'center';
                    socialStatus.style.backgroundColor = '#f8f9fa';
                    socialStatus.style.color = '#333';
                    socialStatus.textContent = 'Connecting to Google...';

                    // Add to the page after the social auth buttons
                    document.querySelector('.social-auth').after(socialStatus);

                    // Disable buttons during "processing"
                    googleSignupBtn.disabled = true;
                    if (facebookSignupBtn) facebookSignupBtn.disabled = true;

                    // Simulate API call delay
                    setTimeout(function() {
                        socialStatus.style.backgroundColor = '#d4edda';
                        socialStatus.style.color = '#155724';
                        socialStatus.textContent = 'Google authentication successful! Redirecting to dashboard...';

                        // Create a mock token and user for Google login
                        const mockToken = 'google_mock_token_' + Date.now();
                        localStorage.setItem('token', mockToken);

                        // Create a mock user with Google info
                        const mockUser = {
                            firstName: 'Google',
                            lastName: 'User',
                            email: '<EMAIL>'
                        };

                        // Store user data
                        localStorage.setItem('user', JSON.stringify(mockUser));
                        localStorage.setItem('userFirstName', mockUser.firstName);
                        localStorage.setItem('userLastName', mockUser.lastName);

                        // Redirect to home page after a short delay
                        setTimeout(function() {
                            window.location.href = '/';
                        }, 1500);
                    }, 2000);
                });
            }

            if (facebookSignupBtn) {
                facebookSignupBtn.addEventListener('click', function() {
                    // Create status message for social login
                    let socialStatus = document.createElement('div');
                    socialStatus.className = 'status-message';
                    socialStatus.style.padding = '10px';
                    socialStatus.style.marginTop = '15px';
                    socialStatus.style.borderRadius = '5px';
                    socialStatus.style.textAlign = 'center';
                    socialStatus.style.backgroundColor = '#f8f9fa';
                    socialStatus.style.color = '#333';
                    socialStatus.textContent = 'Connecting to Facebook...';

                    // Add to the page after the social auth buttons
                    document.querySelector('.social-auth').after(socialStatus);

                    // Disable buttons during "processing"
                    facebookSignupBtn.disabled = true;
                    if (googleSignupBtn) googleSignupBtn.disabled = true;

                    // Simulate API call delay
                    setTimeout(function() {
                        socialStatus.style.backgroundColor = '#d4edda';
                        socialStatus.style.color = '#155724';
                        socialStatus.textContent = 'Facebook authentication successful! Redirecting to dashboard...';

                        // Create a mock token and user for Facebook login
                        const mockToken = 'facebook_mock_token_' + Date.now();
                        localStorage.setItem('token', mockToken);

                        // Create a mock user with Facebook info
                        const mockUser = {
                            firstName: 'Facebook',
                            lastName: 'User',
                            email: '<EMAIL>'
                        };

                        // Store user data
                        localStorage.setItem('user', JSON.stringify(mockUser));
                        localStorage.setItem('userFirstName', mockUser.firstName);
                        localStorage.setItem('userLastName', mockUser.lastName);

                        // Redirect to home page after a short delay
                        setTimeout(function() {
                            window.location.href = '/';
                        }, 1500);
                    }, 2000);
                });
            }
        });
    </script>
</body>
</html>
