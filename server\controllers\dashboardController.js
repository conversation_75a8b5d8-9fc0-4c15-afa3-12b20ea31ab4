const User = require('../models/User');
const Content = require('../models/Content');
const Activity = require('../models/Activity');
const SystemLog = require('../models/SystemLog');

// Get admin dashboard statistics
exports.getDashboardStats = async (req, res, next) => {
  try {
    // Get date for 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get total users count
    const totalUsers = await User.countDocuments();

    // Get new users in last 30 days
    const newUsers = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo },
    });

    // Get total content count
    const totalContent = await Content.countDocuments();

    // Get total page views
    const totalViews = await Content.aggregate([
      {
        $group: {
          _id: null,
          totalViews: { $sum: '$views' },
        },
      },
    ]);

    // Get recent activities
    const recentActivities = await Activity.find()
      .sort('-timestamp')
      .limit(10)
      .populate({
        path: 'user',
        select: 'firstName lastName email',
      });

    // Get system alerts (errors and warnings)
    const systemAlerts = await SystemLog.find({
      level: { $in: ['error', 'warning'] },
      timestamp: { $gte: thirtyDaysAgo },
    })
      .sort('-timestamp')
      .limit(5);

    // Get user registration stats by day for the last 30 days
    const userRegistrationStats = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    // Get content creation stats by day for the last 30 days
    const contentCreationStats = await Content.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    // Get content by type
    const contentByType = await Content.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Get content by category
    const contentByCategory = await Content.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        newUsers,
        totalContent,
        totalViews: totalViews.length > 0 ? totalViews[0].totalViews : 0,
        recentActivities,
        systemAlerts,
        userRegistrationStats,
        contentCreationStats,
        contentByType,
        contentByCategory,
      },
    });
  } catch (error) {
    next(error);
  }
};
