const express = require('express');
const settingsController = require('../controllers/settingsController');
const { protect, restrictTo } = require('../middleware/auth');

const router = express.Router();

// Protect all routes
router.use(protect);

// Routes for both admin and regular users
router.get('/:key', settingsController.getSettingByKey);

// Admin-only routes
router.get('/', restrictTo('admin'), settingsController.getAllSettings);
router.patch('/:key', restrictTo('admin'), settingsController.updateSetting);
router.patch('/', restrictTo('admin'), settingsController.updateMultipleSettings);

module.exports = router;
