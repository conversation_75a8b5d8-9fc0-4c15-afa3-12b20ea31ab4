<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile | MATERNIFY</title>
    <meta name="description" content="Manage your profile information and account settings.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Custom styles for header to match footer -->
    <style>
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        .user-menu {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        .user-greeting {
            color: var(--white) !important;
        }
        .logout-btn {
            color: var(--white) !important;
        }
        .logout-btn:hover {
            color: var(--primary-color) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }
    </style>
    <style>
        /* Dashboard Layout */
        .dashboard-layout {
            display: flex;
            min-height: calc(100vh - 70px - 300px); /* Adjust based on header and footer height */
        }

        /* Sidebar Styles */
        .dashboard-sidebar {
            width: 250px;
            background-color: var(--text-color); /* Match header color */
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: var(--spacing-md);
            position: sticky;
            top: 70px; /* Adjust based on header height */
            height: calc(100vh - 70px); /* Adjust based on header height */
            overflow-y: auto;
            transition: transform 0.3s ease;
            color: var(--white);
        }

        .sidebar-header {
            padding-bottom: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .sidebar-user {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-weight: 600;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: var(--white);
        }

        .user-role {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .sidebar-nav {
            margin-bottom: var(--spacing-lg);
        }

        .sidebar-section {
            margin-bottom: var(--spacing-md);
        }

        .sidebar-section-title {
            font-size: 0.9rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.5);
            margin-bottom: var(--spacing-sm);
            padding-left: var(--spacing-sm);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu-item {
            margin-bottom: 2px;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color);
        }

        .sidebar-menu-link.active {
            background-color: rgba(255, 255, 255, 0.15);
            color: var(--primary-color);
            font-weight: 500;
        }

        .sidebar-menu-icon {
            margin-right: var(--spacing-sm);
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            padding-top: var(--spacing-md);
            margin-top: auto;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-footer .logout-btn {
            display: flex;
            align-items: center;
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .sidebar-footer .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color);
        }

        .logout-icon {
            margin-right: var(--spacing-sm);
        }

        /* Main Content Styles */
        .dashboard-main {
            flex: 1;
            padding: var(--spacing-xl);
            background-color: var(--white);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
        }

        .dashboard-title {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .dashboard-welcome {
            font-size: 1rem;
            color: var(--text-light);
        }

        .dashboard-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* Mobile Toggle Button */
        .sidebar-toggle {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            box-shadow: var(--shadow-md);
            z-index: 100;
            cursor: pointer;
            align-items: center;
            justify-content: center;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .dashboard-sidebar {
                position: fixed;
                left: 0;
                z-index: 1000;
                transform: translateX(-100%);
                box-shadow: var(--shadow-md);
            }

            .dashboard-sidebar.active {
                transform: translateX(0);
            }

            .sidebar-toggle {
                display: flex;
            }

            .dashboard-main {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .dashboard-actions {
                width: 100%;
            }

            .dashboard-actions .btn {
                flex: 1;
                text-align: center;
            }
        }

        /* Profile Page Styles */
        .profile-section {
            margin-bottom: var(--spacing-xl);
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
        }

        .profile-section-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--border-color);
        }

        .profile-section-icon {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-md);
            color: var(--white);
            font-size: 1.2rem;
        }

        .profile-section-title {
            font-size: 1.3rem;
            color: var(--text-color);
            margin-bottom: 0;
        }

        .profile-form-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .profile-form-group {
            flex: 1;
            min-width: 250px;
        }

        .profile-form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
            color: var(--text-color);
        }

        .profile-form-group input,
        .profile-form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-family: var(--body-font);
            transition: border-color 0.3s ease;
        }

        .profile-form-group input:focus,
        .profile-form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(248, 198, 209, 0.2);
        }

        .profile-actions {
            display: flex;
            justify-content: flex-end;
            margin-top: var(--spacing-lg);
            gap: var(--spacing-md);
        }

        .btn-save {
            background-color: var(--primary-color);
            color: var(--text-color);
            font-weight: 600;
        }

        .btn-save:hover {
            background-color: var(--primary-dark);
            color: var(--white);
        }

        .btn-cancel {
            background-color: var(--light-bg);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-cancel:hover {
            background-color: var(--border-color);
        }

        .status-message {
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-bottom: var(--spacing-md);
            display: none;
        }

        .status-message.success {
            background-color: var(--success-color);
            color: #155724;
        }

        .status-message.error {
            background-color: var(--error-color);
            color: #721c24;
        }

        .child-info-container {
            margin-top: var(--spacing-md);
            border-top: 1px dashed var(--border-color);
            padding-top: var(--spacing-md);
        }

        .child-info-item {
            background-color: var(--light-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            position: relative;
        }

        .remove-child-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: var(--error-color);
            cursor: pointer;
            font-size: 1rem;
        }

        .add-child-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: none;
            border: 1px dashed var(--border-color);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: var(--primary-dark);
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .add-child-btn:hover {
            background-color: rgba(248, 198, 209, 0.1);
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li>
                        <div class="user-menu">
                            <span class="user-greeting">Hello, <span id="nav-user-name" class="user-name">User</span>!</span>
                            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Mobile Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="dashboardSidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">MATERNIFY</h3>
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <span id="user-initial">U</span>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="sidebar-user-name">User</div>
                        <div class="user-role">Member</div>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <!-- Main Navigation -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Main</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/dashboard" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-home"></i></span>
                                Dashboard
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/profile" class="sidebar-menu-link active">
                                <span class="sidebar-menu-icon"><i class="fas fa-user"></i></span>
                                My Profile
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/pages/health-journal.html" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-notes-medical"></i></span>
                                Health Journal
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/pages/video-resources.html" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-video"></i></span>
                                Video Resources
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Pregnancy Services -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Pregnancy</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/pregnancy-care" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-heart"></i></span>
                                Pregnancy Care
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#due-date" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-calendar-alt"></i></span>
                                Due Date Calculator
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#bmi" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-weight"></i></span>
                                BMI Tracker
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/pregnancy-care#nutrition" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-apple-alt"></i></span>
                                Nutrition Guide
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/pregnancy-care#exercise" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-running"></i></span>
                                Exercise Tips
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Infant Care -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Infant Care</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/infant-care" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-baby"></i></span>
                                Infant Care Guide
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#growth-tracker" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-chart-line"></i></span>
                                Growth Tracker
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#vaccination" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-syringe"></i></span>
                                Vaccination Schedule
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/infant-care#milestones" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-flag"></i></span>
                                Milestones
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Community -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Community</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/community" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-users"></i></span>
                                Community Forum
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/blog" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-book-open"></i></span>
                                Articles & Blogs
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="sidebar-footer">
                <a href="#" class="logout-btn" id="sidebarLogoutBtn">
                    <span class="logout-icon"><i class="fas fa-sign-out-alt"></i></span>
                    Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <div class="dashboard-header">
                <div>
                    <h1 class="dashboard-title">My Profile</h1>
                    <p class="dashboard-welcome">Manage your personal information and account settings</p>
                </div>
                <div class="dashboard-actions">
                    <a href="/dashboard" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Status Messages -->
            <div id="profileUpdateSuccess" class="status-message success">
                <i class="fas fa-check-circle"></i> Your profile has been updated successfully.
            </div>
            <div id="profileUpdateError" class="status-message error">
                <i class="fas fa-exclamation-circle"></i> <span id="profileUpdateErrorMessage">An error occurred while updating your profile.</span>
            </div>
            <div id="passwordUpdateSuccess" class="status-message success">
                <i class="fas fa-check-circle"></i> Your password has been updated successfully.
            </div>
            <div id="passwordUpdateError" class="status-message error">
                <i class="fas fa-exclamation-circle"></i> <span id="passwordUpdateErrorMessage">An error occurred while updating your password.</span>
            </div>

            <!-- Personal Information Section -->
            <section class="profile-section">
                <div class="profile-section-header">
                    <div class="profile-section-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <h2 class="profile-section-title">Personal Information</h2>
                </div>
                <form id="personalInfoForm">
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="firstName">First Name</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="profile-form-group">
                            <label for="lastName">Last Name</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" required readonly>
                            <small>Email cannot be changed. Contact support for assistance.</small>
                        </div>
                        <div class="profile-form-group">
                            <label for="phoneNumber">Phone Number</label>
                            <input type="tel" id="phoneNumber" name="phoneNumber" placeholder="10-digit phone number">
                        </div>
                    </div>
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="dateOfBirth">Date of Birth</label>
                            <input type="date" id="dateOfBirth" name="dateOfBirth">
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button type="button" class="btn btn-cancel" id="cancelPersonalInfo">Cancel</button>
                        <button type="submit" class="btn btn-save">Save Changes</button>
                    </div>
                </form>
            </section>

            <!-- Address Information Section -->
            <section class="profile-section">
                <div class="profile-section-header">
                    <div class="profile-section-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h2 class="profile-section-title">Address Information</h2>
                </div>
                <form id="addressInfoForm">
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="street">Street Address</label>
                            <input type="text" id="street" name="street">
                        </div>
                    </div>
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="city">City</label>
                            <input type="text" id="city" name="city">
                        </div>
                        <div class="profile-form-group">
                            <label for="state">State/Province</label>
                            <input type="text" id="state" name="state">
                        </div>
                    </div>
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="zipCode">Zip/Postal Code</label>
                            <input type="text" id="zipCode" name="zipCode">
                        </div>
                        <div class="profile-form-group">
                            <label for="country">Country</label>
                            <input type="text" id="country" name="country">
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button type="button" class="btn btn-cancel" id="cancelAddressInfo">Cancel</button>
                        <button type="submit" class="btn btn-save">Save Changes</button>
                    </div>
                </form>
            </section>

            <!-- Pregnancy & Child Information Section -->
            <section class="profile-section">
                <div class="profile-section-header">
                    <div class="profile-section-icon">
                        <i class="fas fa-baby-carriage"></i>
                    </div>
                    <h2 class="profile-section-title">Pregnancy & Child Information</h2>
                </div>
                <form id="pregnancyChildInfoForm">
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="pregnancyDueDate">Pregnancy Due Date</label>
                            <input type="date" id="pregnancyDueDate" name="pregnancyDueDate">
                            <small>Leave blank if not currently pregnant</small>
                        </div>
                    </div>

                    <h3>Children Information</h3>
                    <div id="childrenContainer" class="child-info-container">
                        <!-- Child info items will be added here dynamically -->
                    </div>

                    <button type="button" id="addChildBtn" class="add-child-btn">
                        <i class="fas fa-plus"></i> Add Child
                    </button>

                    <div class="profile-actions">
                        <button type="button" class="btn btn-cancel" id="cancelPregnancyChildInfo">Cancel</button>
                        <button type="submit" class="btn btn-save">Save Changes</button>
                    </div>
                </form>
            </section>

            <!-- Account Settings Section -->
            <section class="profile-section">
                <div class="profile-section-header">
                    <div class="profile-section-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h2 class="profile-section-title">Account Settings</h2>
                </div>
                <form id="passwordChangeForm">
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="currentPassword">Current Password</label>
                            <input type="password" id="currentPassword" name="currentPassword" required>
                        </div>
                    </div>
                    <div class="profile-form-row">
                        <div class="profile-form-group">
                            <label for="newPassword">New Password</label>
                            <input type="password" id="newPassword" name="newPassword" required>
                            <small>Password must be at least 6 characters long</small>
                        </div>
                        <div class="profile-form-group">
                            <label for="confirmPassword">Confirm New Password</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button type="button" class="btn btn-cancel" id="cancelPasswordChange">Cancel</button>
                        <button type="submit" class="btn btn-save">Update Password</button>
                    </div>
                </form>
            </section>
        </main>
    </div>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                        <li><a href="/infant-care">Infant Care</a></li>
                        <li><a href="/tools">Tools</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/blog">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/tools#due-date">Due Date Calculator</a></li>
                        <li><a href="/tools#bmi">BMI Tracker</a></li>
                        <li><a href="/tools#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/tools#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/logout.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            // Check if API service is available
            if (!window.apiService) {
                console.error('API service not found. Make sure api-service.js is loaded before this script');
                return;
            }

            // Check if user is logged in
            const token = localStorage.getItem('token');
            if (!token) {
                // Redirect to login page if not logged in
                window.location.href = '/login';
                return;
            }

            // Get user data from localStorage
            let userData = JSON.parse(localStorage.getItem('user')) || {};
            const firstName = localStorage.getItem('userFirstName') || userData.firstName || 'User';
            const lastName = localStorage.getItem('userLastName') || userData.lastName || '';

            // Update UI with user name
            document.getElementById('nav-user-name').textContent = firstName;
            document.getElementById('sidebar-user-name').textContent = `${firstName} ${lastName}`;
            document.getElementById('user-name').textContent = `${firstName} ${lastName}`;
            document.getElementById('user-initial').textContent = firstName.charAt(0).toUpperCase();

            // Populate form fields with user data
            populateUserData(userData);

            // Handle sidebar toggle for mobile
            const sidebarToggle = document.getElementById('sidebarToggle');
            const dashboardSidebar = document.getElementById('dashboardSidebar');

            if (sidebarToggle && dashboardSidebar) {
                sidebarToggle.addEventListener('click', function() {
                    dashboardSidebar.classList.toggle('active');
                    this.classList.toggle('active');
                });
            }

            // Handle form submissions
            setupFormHandlers();

            // Handle child info functionality
            setupChildInfoHandlers();
        });

        // Populate form fields with user data
        function populateUserData(userData) {
            // Personal Information
            document.getElementById('firstName').value = userData.firstName || '';
            document.getElementById('lastName').value = userData.lastName || '';
            document.getElementById('email').value = userData.email || '';
            document.getElementById('phoneNumber').value = userData.phoneNumber || '';

            if (userData.dateOfBirth) {
                const dob = new Date(userData.dateOfBirth);
                document.getElementById('dateOfBirth').value = dob.toISOString().split('T')[0];
            }

            // Address Information
            if (userData.address) {
                document.getElementById('street').value = userData.address.street || '';
                document.getElementById('city').value = userData.address.city || '';
                document.getElementById('state').value = userData.address.state || '';
                document.getElementById('zipCode').value = userData.address.zipCode || '';
                document.getElementById('country').value = userData.address.country || '';
            }

            // Pregnancy Due Date
            if (userData.pregnancyDueDate) {
                const dueDate = new Date(userData.pregnancyDueDate);
                document.getElementById('pregnancyDueDate').value = dueDate.toISOString().split('T')[0];
            } else {
                // Check localStorage for due date
                const storedDueDate = localStorage.getItem('pregnancyDueDate');
                if (storedDueDate) {
                    document.getElementById('pregnancyDueDate').value = storedDueDate;
                }
            }

            // Children Information
            const childrenContainer = document.getElementById('childrenContainer');
            childrenContainer.innerHTML = ''; // Clear container

            if (userData.childrenInfo && userData.childrenInfo.length > 0) {
                userData.childrenInfo.forEach((child, index) => {
                    addChildInfoItem(child, index);
                });
            } else {
                // Check localStorage for baby birthdate
                const babyBirthdate = localStorage.getItem('babyBirthdate');
                if (babyBirthdate) {
                    addChildInfoItem({ dateOfBirth: babyBirthdate }, 0);
                }
            }
        }

        // Add child info item to the form
        function addChildInfoItem(childData = {}, index) {
            const childrenContainer = document.getElementById('childrenContainer');
            const childItem = document.createElement('div');
            childItem.className = 'child-info-item';
            childItem.dataset.index = index;

            const dateValue = childData.dateOfBirth ? new Date(childData.dateOfBirth).toISOString().split('T')[0] : '';

            childItem.innerHTML = `
                <button type="button" class="remove-child-btn" data-index="${index}">
                    <i class="fas fa-times"></i>
                </button>
                <div class="profile-form-row">
                    <div class="profile-form-group">
                        <label for="childName${index}">Child's Name</label>
                        <input type="text" id="childName${index}" name="childName${index}" value="${childData.name || ''}">
                    </div>
                    <div class="profile-form-group">
                        <label for="childDob${index}">Date of Birth</label>
                        <input type="date" id="childDob${index}" name="childDob${index}" value="${dateValue}">
                    </div>
                    <div class="profile-form-group">
                        <label for="childGender${index}">Gender</label>
                        <select id="childGender${index}" name="childGender${index}">
                            <option value="">Select Gender</option>
                            <option value="male" ${childData.gender === 'male' ? 'selected' : ''}>Male</option>
                            <option value="female" ${childData.gender === 'female' ? 'selected' : ''}>Female</option>
                            <option value="other" ${childData.gender === 'other' ? 'selected' : ''}>Other</option>
                        </select>
                    </div>
                </div>
            `;

            childrenContainer.appendChild(childItem);

            // Add event listener to remove button
            const removeBtn = childItem.querySelector('.remove-child-btn');
            removeBtn.addEventListener('click', function() {
                childItem.remove();
            });
        }

        // Setup child info handlers
        function setupChildInfoHandlers() {
            const addChildBtn = document.getElementById('addChildBtn');

            addChildBtn.addEventListener('click', function() {
                const childrenContainer = document.getElementById('childrenContainer');
                const childCount = childrenContainer.children.length;
                addChildInfoItem({}, childCount);
            });
        }

        // Setup form handlers
        function setupFormHandlers() {
            // Personal Information Form
            const personalInfoForm = document.getElementById('personalInfoForm');
            personalInfoForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = {
                    firstName: document.getElementById('firstName').value,
                    lastName: document.getElementById('lastName').value,
                    phoneNumber: document.getElementById('phoneNumber').value,
                    dateOfBirth: document.getElementById('dateOfBirth').value || null
                };

                try {
                    await updateUserProfile(formData);
                    showStatusMessage('profileUpdateSuccess');
                } catch (error) {
                    document.getElementById('profileUpdateErrorMessage').textContent = error.message;
                    showStatusMessage('profileUpdateError');
                }
            });

            // Address Information Form
            const addressInfoForm = document.getElementById('addressInfoForm');
            addressInfoForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = {
                    address: {
                        street: document.getElementById('street').value,
                        city: document.getElementById('city').value,
                        state: document.getElementById('state').value,
                        zipCode: document.getElementById('zipCode').value,
                        country: document.getElementById('country').value
                    }
                };

                try {
                    await updateUserProfile(formData);
                    showStatusMessage('profileUpdateSuccess');
                } catch (error) {
                    document.getElementById('profileUpdateErrorMessage').textContent = error.message;
                    showStatusMessage('profileUpdateError');
                }
            });

            // Pregnancy & Child Information Form
            const pregnancyChildInfoForm = document.getElementById('pregnancyChildInfoForm');
            pregnancyChildInfoForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const pregnancyDueDate = document.getElementById('pregnancyDueDate').value;

                // Collect children info
                const childrenContainer = document.getElementById('childrenContainer');
                const childItems = childrenContainer.querySelectorAll('.child-info-item');
                const childrenInfo = [];

                childItems.forEach((item, index) => {
                    const name = document.getElementById(`childName${index}`).value;
                    const dob = document.getElementById(`childDob${index}`).value;
                    const gender = document.getElementById(`childGender${index}`).value;

                    if (name || dob || gender) {
                        childrenInfo.push({
                            name,
                            dateOfBirth: dob || null,
                            gender: gender || null
                        });
                    }
                });

                const formData = {
                    pregnancyDueDate: pregnancyDueDate || null,
                    childrenInfo
                };

                try {
                    await updateUserProfile(formData);

                    // Update localStorage
                    if (pregnancyDueDate) {
                        localStorage.setItem('pregnancyDueDate', pregnancyDueDate);
                    } else {
                        localStorage.removeItem('pregnancyDueDate');
                    }

                    showStatusMessage('profileUpdateSuccess');
                } catch (error) {
                    document.getElementById('profileUpdateErrorMessage').textContent = error.message;
                    showStatusMessage('profileUpdateError');
                }
            });

            // Password Change Form
            const passwordChangeForm = document.getElementById('passwordChangeForm');
            passwordChangeForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const currentPassword = document.getElementById('currentPassword').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                // Validate passwords
                if (newPassword.length < 6) {
                    document.getElementById('passwordUpdateErrorMessage').textContent = 'Password must be at least 6 characters long';
                    showStatusMessage('passwordUpdateError');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    document.getElementById('passwordUpdateErrorMessage').textContent = 'Passwords do not match';
                    showStatusMessage('passwordUpdateError');
                    return;
                }

                try {
                    await updateUserPassword(currentPassword, newPassword);

                    // Clear password fields
                    document.getElementById('currentPassword').value = '';
                    document.getElementById('newPassword').value = '';
                    document.getElementById('confirmPassword').value = '';

                    showStatusMessage('passwordUpdateSuccess');
                } catch (error) {
                    document.getElementById('passwordUpdateErrorMessage').textContent = error.message;
                    showStatusMessage('passwordUpdateError');
                }
            });

            // Cancel buttons
            document.getElementById('cancelPersonalInfo').addEventListener('click', function() {
                const userData = JSON.parse(localStorage.getItem('user')) || {};
                document.getElementById('firstName').value = userData.firstName || '';
                document.getElementById('lastName').value = userData.lastName || '';
                document.getElementById('phoneNumber').value = userData.phoneNumber || '';

                if (userData.dateOfBirth) {
                    const dob = new Date(userData.dateOfBirth);
                    document.getElementById('dateOfBirth').value = dob.toISOString().split('T')[0];
                } else {
                    document.getElementById('dateOfBirth').value = '';
                }
            });

            document.getElementById('cancelAddressInfo').addEventListener('click', function() {
                const userData = JSON.parse(localStorage.getItem('user')) || {};

                if (userData.address) {
                    document.getElementById('street').value = userData.address.street || '';
                    document.getElementById('city').value = userData.address.city || '';
                    document.getElementById('state').value = userData.address.state || '';
                    document.getElementById('zipCode').value = userData.address.zipCode || '';
                    document.getElementById('country').value = userData.address.country || '';
                } else {
                    document.getElementById('street').value = '';
                    document.getElementById('city').value = '';
                    document.getElementById('state').value = '';
                    document.getElementById('zipCode').value = '';
                    document.getElementById('country').value = '';
                }
            });

            document.getElementById('cancelPregnancyChildInfo').addEventListener('click', function() {
                const userData = JSON.parse(localStorage.getItem('user')) || {};

                if (userData.pregnancyDueDate) {
                    const dueDate = new Date(userData.pregnancyDueDate);
                    document.getElementById('pregnancyDueDate').value = dueDate.toISOString().split('T')[0];
                } else {
                    const storedDueDate = localStorage.getItem('pregnancyDueDate');
                    document.getElementById('pregnancyDueDate').value = storedDueDate || '';
                }

                // Reset children info
                const childrenContainer = document.getElementById('childrenContainer');
                childrenContainer.innerHTML = '';

                if (userData.childrenInfo && userData.childrenInfo.length > 0) {
                    userData.childrenInfo.forEach((child, index) => {
                        addChildInfoItem(child, index);
                    });
                } else {
                    const babyBirthdate = localStorage.getItem('babyBirthdate');
                    if (babyBirthdate) {
                        addChildInfoItem({ dateOfBirth: babyBirthdate }, 0);
                    }
                }
            });

            document.getElementById('cancelPasswordChange').addEventListener('click', function() {
                document.getElementById('currentPassword').value = '';
                document.getElementById('newPassword').value = '';
                document.getElementById('confirmPassword').value = '';
            });
        }

        // Update user profile
        async function updateUserProfile(profileData) {
            try {
                // Call API to update profile
                const result = await window.apiService.updateProfile(profileData);

                if (result.success) {
                    // Update local storage with new data
                    let userData = JSON.parse(localStorage.getItem('user')) || {};
                    userData = { ...userData, ...profileData };

                    // Update specific fields
                    if (profileData.firstName) {
                        localStorage.setItem('userFirstName', profileData.firstName);
                        document.getElementById('nav-user-name').textContent = profileData.firstName;
                        document.getElementById('user-initial').textContent = profileData.firstName.charAt(0).toUpperCase();
                    }

                    if (profileData.lastName) {
                        localStorage.setItem('userLastName', profileData.lastName);
                    }

                    if (profileData.firstName || profileData.lastName) {
                        const firstName = profileData.firstName || userData.firstName;
                        const lastName = profileData.lastName || userData.lastName;
                        document.getElementById('sidebar-user-name').textContent = `${firstName} ${lastName}`;
                        document.getElementById('user-name').textContent = `${firstName} ${lastName}`;
                    }

                    localStorage.setItem('user', JSON.stringify(userData));
                    return result;
                } else {
                    throw new Error(result.message || 'Failed to update profile');
                }
            } catch (error) {
                console.error('Profile update error:', error);
                throw new Error(error.message || 'An error occurred while updating your profile');
            }
        }

        // Update user password
        async function updateUserPassword(currentPassword, newPassword) {
            try {
                // Call API to update password
                const result = await window.apiService.updatePassword(currentPassword, newPassword);

                if (result.success) {
                    return result;
                } else {
                    throw new Error(result.message || 'Failed to update password');
                }
            } catch (error) {
                console.error('Password update error:', error);
                throw new Error(error.message || 'An error occurred while updating your password');
            }
        }

        // Show status message
        function showStatusMessage(messageId) {
            // Hide all status messages
            const statusMessages = document.querySelectorAll('.status-message');
            statusMessages.forEach(message => {
                message.style.display = 'none';
            });

            // Show the specified message
            const message = document.getElementById(messageId);
            if (message) {
                message.style.display = 'block';

                // Scroll to message
                message.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Hide message after 5 seconds
                setTimeout(() => {
                    message.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
