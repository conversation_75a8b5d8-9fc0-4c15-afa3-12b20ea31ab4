<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard | MATERNIFY</title>
    <meta name="description" content="Your personal dashboard for tracking pregnancy and infant care.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Custom styles for header to match footer -->
    <style>
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        .user-menu {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        .user-greeting {
            color: var(--white) !important;
        }
        .logout-btn {
            color: var(--white) !important;
        }
        .logout-btn:hover {
            color: var(--primary-color) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }
    </style>
    <style>
        /* Dashboard Layout */
        .dashboard-layout {
            display: flex;
            min-height: calc(100vh - 70px - 300px); /* Adjust based on header and footer height */
        }

        /* Sidebar Styles */
        .dashboard-sidebar {
            width: 250px;
            background-color: var(--text-color); /* Match header color */
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: var(--spacing-md);
            position: sticky;
            top: 70px; /* Adjust based on header height */
            height: calc(100vh - 70px); /* Adjust based on header height */
            overflow-y: auto;
            transition: transform 0.3s ease;
            color: var(--white);
        }

        .sidebar-header {
            padding-bottom: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            font-size: 1.2rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .sidebar-user {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            font-weight: 600;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: var(--white);
        }

        .user-role {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .sidebar-nav {
            margin-bottom: var(--spacing-lg);
        }

        .sidebar-section {
            margin-bottom: var(--spacing-md);
        }

        .sidebar-section-title {
            font-size: 0.9rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.5);
            margin-bottom: var(--spacing-sm);
            padding-left: var(--spacing-sm);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu-item {
            margin-bottom: 2px;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color);
        }

        .sidebar-menu-link.active {
            background-color: rgba(255, 255, 255, 0.15);
            color: var(--primary-color);
            font-weight: 500;
        }

        .sidebar-menu-icon {
            margin-right: var(--spacing-sm);
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            padding-top: var(--spacing-md);
            margin-top: auto;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-footer .logout-btn {
            display: flex;
            align-items: center;
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .sidebar-footer .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color);
        }

        .logout-icon {
            margin-right: var(--spacing-sm);
        }

        /* Main Content Styles */
        .dashboard-main {
            flex: 1;
            padding: var(--spacing-xl);
            background-color: var(--white);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
        }

        .dashboard-title {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .dashboard-welcome {
            font-size: 1rem;
            color: var(--text-light);
        }

        .dashboard-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .dashboard-card {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-lg);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
        }

        .dashboard-card-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-md);
        }

        .dashboard-card-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-right: var(--spacing-sm);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(248, 198, 209, 0.1);
            border-radius: 50%;
        }

        .dashboard-card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .dashboard-card-content {
            color: var(--text-light);
        }

        .dashboard-card-footer {
            margin-top: var(--spacing-md);
            display: flex;
            justify-content: flex-end;
        }

        .dashboard-card-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
        }

        .dashboard-card-link:hover {
            color: var(--primary-dark);
        }

        .dashboard-card-link i {
            margin-left: var(--spacing-xs);
            font-size: 0.8rem;
        }

        /* Mobile Toggle Button */
        .sidebar-toggle {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            box-shadow: var(--shadow-md);
            z-index: 100;
            cursor: pointer;
            align-items: center;
            justify-content: center;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .dashboard-sidebar {
                position: fixed;
                left: 0;
                z-index: 1000;
                transform: translateX(-100%);
                box-shadow: var(--shadow-md);
            }

            .dashboard-sidebar.active {
                transform: translateX(0);
            }

            .sidebar-toggle {
                display: flex;
            }

            .dashboard-main {
                width: 100%;
            }

            .dashboard-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .dashboard-actions {
                width: 100%;
            }

            .dashboard-actions .btn {
                flex: 1;
                text-align: center;
            }
        }

        /* User menu in navigation */
        .nav-user-menu {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background-color: var(--light-bg);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-md);
        }

        .nav-user-greeting {
            font-weight: 500;
            color: var(--text-color);
        }

        .nav-logout-btn {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
        }

        .nav-logout-btn:hover {
            color: var(--primary-dark);
        }

        @media (max-width: 768px) {
            .nav-user-menu {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li>
                        <div class="user-menu">
                            <span class="user-greeting">Hello, <span id="nav-user-name" class="user-name">User</span>!</span>
                            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Mobile Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="dashboardSidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">MATERNIFY</h3>
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <span id="user-initial">U</span>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="sidebar-user-name">User</div>
                        <div class="user-role">Member</div>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <!-- Main Navigation -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Main</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/dashboard" class="sidebar-menu-link active">
                                <span class="sidebar-menu-icon"><i class="fas fa-home"></i></span>
                                Dashboard
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/profile" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-user"></i></span>
                                My Profile
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Pregnancy Services -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Pregnancy</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/pregnancy-care" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-heart"></i></span>
                                Pregnancy Care
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#due-date" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-calendar-alt"></i></span>
                                Due Date Calculator
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#bmi" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-weight"></i></span>
                                BMI Tracker
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/pregnancy-care#nutrition" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-apple-alt"></i></span>
                                Nutrition Guide
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/pregnancy-care#exercise" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-running"></i></span>
                                Exercise Tips
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Infant Care Services -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Infant Care</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/infant-care" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-baby"></i></span>
                                Infant Care
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#growth-tracker" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-chart-line"></i></span>
                                Growth Tracker
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/tools#vaccination" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-syringe"></i></span>
                                Vaccination Schedule
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/infant-care#breastfeeding" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-baby-carriage"></i></span>
                                Breastfeeding Guide
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/infant-care#sleep" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-moon"></i></span>
                                Sleep Patterns
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Community & Resources -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Community</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/community" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-users"></i></span>
                                Community Forum
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/blog" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-book-open"></i></span>
                                Articles & Blog
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/health-journal" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-notes-medical"></i></span>
                                Health Journal
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="/video-resources" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-video"></i></span>
                                Video Resources
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Admin section - only visible for admin users -->
                <div class="sidebar-section admin-section" id="adminSection" style="display: none;">
                    <h4 class="sidebar-section-title">Administration</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/admin-dashboard" class="sidebar-menu-link">
                                <span class="sidebar-menu-icon"><i class="fas fa-tachometer-alt"></i></span>
                                Admin Dashboard
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="sidebar-footer">
                <a href="#" class="logout-btn" id="sidebarLogoutBtn">
                    <span class="logout-icon"><i class="fas fa-sign-out-alt"></i></span>
                    Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <div class="dashboard-header">
                <div>
                    <h1 class="dashboard-title">Dashboard</h1>
                    <p class="dashboard-welcome">Welcome back, <span id="user-name" class="user-name">User</span>!</p>
                </div>
                <div class="dashboard-actions">
                    <a href="/tools" class="btn btn-primary">Explore Tools</a>
                    <a href="/community" class="btn btn-secondary">Join Community</a>
                    <!-- Admin button - only visible for admin users -->
                    <a href="/admin-dashboard" id="adminHeaderBtn" class="btn" style="display: none; background-color: #4a4a4a; color: white;"><i class="fas fa-user-shield"></i> Admin Panel</a>
                </div>
            </div>

            <div class="dashboard-grid">
                <!-- Featured Tools Section -->
                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h2 class="dashboard-card-title">Due Date Calculator</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Calculate your expected due date based on your last menstrual period.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/tools#due-date" class="dashboard-card-link">Calculate Now <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-weight"></i>
                        </div>
                        <h2 class="dashboard-card-title">BMI Tracker</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Monitor your Body Mass Index (BMI) throughout your pregnancy.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/tools#bmi" class="dashboard-card-link">Check BMI <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h2 class="dashboard-card-title">Growth Tracker</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Track your baby's growth and development milestones with visual charts.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/tools#growth-tracker" class="dashboard-card-link">Track Growth <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-syringe"></i>
                        </div>
                        <h2 class="dashboard-card-title">Vaccination Schedule</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Keep track of your baby's vaccination schedule and upcoming appointments.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/tools#vaccination" class="dashboard-card-link">View Schedule <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-notes-medical"></i>
                        </div>
                        <h2 class="dashboard-card-title">Health Journal</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Track your pregnancy symptoms, appointments, and baby's health milestones.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/health-journal" class="dashboard-card-link">Open Journal <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h2 class="dashboard-card-title">Community</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Connect with other parents and share your experiences in our supportive community.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/community" class="dashboard-card-link">Join Now <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h2 class="dashboard-card-title">Video Resources</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Watch educational videos on pregnancy, childbirth, infant care, and parenting.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/video-resources" class="dashboard-card-link">Watch Videos <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- Admin Dashboard Card - Only visible for admin users -->
                <div class="dashboard-card" id="adminDashboardCard" style="display: none; background-color: #f8f9fa; border: 2px solid #4a4a4a;">
                    <div class="dashboard-card-header">
                        <div class="dashboard-card-icon" style="background-color: #4a4a4a; color: white;">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h2 class="dashboard-card-title">Admin Dashboard</h2>
                    </div>
                    <div class="dashboard-card-content">
                        <p>Access the admin panel to manage users, content, settings, and view system analytics.</p>
                    </div>
                    <div class="dashboard-card-footer">
                        <a href="/admin-dashboard" class="dashboard-card-link" style="color: #4a4a4a; font-weight: bold;">Open Admin Panel <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                        <li><a href="/infant-care">Infant Care</a></li>
                        <li><a href="/tools">Tools</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/blog">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/tools#due-date">Due Date Calculator</a></li>
                        <li><a href="/tools#bmi">BMI Tracker</a></li>
                        <li><a href="/tools#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/tools#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/logout.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            // Check if API service is available
            if (!window.apiService) {
                console.error('API service not found. Make sure api-service.js is loaded before this script');
                return;
            }

            // Check if user is logged in
            const token = localStorage.getItem('token');
            if (!token) {
                // Redirect to login page if not logged in (using clean URL)
                window.location.href = '/login';
                return;
            }

            // Try to get user data from localStorage first for faster rendering
            const userFirstName = localStorage.getItem('userFirstName') || 'User';

            // Function to update all user name elements on the page
            function updateAllUserNameElements(name) {
                console.log('Updating all user name elements with:', name);

                // Debug: Log all user name elements on the page
                console.log('nav-user-name element:', document.getElementById('nav-user-name'));
                console.log('Elements with class user-name:', document.querySelectorAll('.user-name'));

                // Update specific ID elements
                const elementsById = {
                    'nav-user-name': document.getElementById('nav-user-name'),
                    'user-name': document.getElementById('user-name'),
                    'sidebar-user-name': document.getElementById('sidebar-user-name')
                };

                // Update each element if it exists
                Object.entries(elementsById).forEach(([id, element]) => {
                    if (element) {
                        console.log(`Updating element with ID ${id} to: ${name}`);
                        element.textContent = name;
                    } else {
                        console.warn(`Element with ID ${id} not found`);
                    }
                });

                // Also update all elements with class 'user-name'
                const userNameElements = document.querySelectorAll('.user-name');
                userNameElements.forEach(element => {
                    element.textContent = name;
                });

                // Set user initial for avatar
                const userInitial = document.getElementById('user-initial');
                if (userInitial) {
                    userInitial.textContent = name.charAt(0).toUpperCase();
                }
            }

            // Update user name elements with data from localStorage
            if (userFirstName) {
                updateAllUserNameElements(userFirstName);
            }

            try {
                console.log('Attempting to get user data for dashboard');

                // First try to use the stored user data
                if (userFirstName) {
                    console.log('Using stored user data:', userFirstName);

                    // Update all user name elements using our helper function
                    updateAllUserNameElements(userFirstName);

                    // No need to make an API call if we have the data
                    console.log('Dashboard loaded with stored user data');

                    // Check if user is an admin from localStorage
                    const storedUser = JSON.parse(localStorage.getItem('user')) || {};
                    if (storedUser.role === 'admin') {
                        // Show admin section in sidebar
                        const adminSection = document.getElementById('adminSection');
                        if (adminSection) {
                            adminSection.style.display = 'block';
                        }

                        // Show admin dashboard card
                        const adminDashboardCard = document.getElementById('adminDashboardCard');
                        if (adminDashboardCard) {
                            adminDashboardCard.style.display = 'block';
                        }

                        // Show admin header button
                        const adminHeaderBtn = document.getElementById('adminHeaderBtn');
                        if (adminHeaderBtn) {
                            adminHeaderBtn.style.display = 'inline-flex';
                        }
                    }
                } else {
                    // If no stored data, try to get it from the API
                    console.log('No stored user data, calling API');

                    // Get current user using the API service to ensure data is up-to-date
                    const result = await apiService.getCurrentUser();

                    if (result.success) {
                        const firstName = result.data.firstName || 'User';

                        console.log('API returned user data:', firstName);

                        // Update all user name elements using our helper function
                        updateAllUserNameElements(firstName);

                        // Store updated user data
                        localStorage.setItem('user', JSON.stringify(result.data));
                        if (result.data.firstName) {
                            localStorage.setItem('userFirstName', result.data.firstName);
                        }
                        if (result.data.lastName) {
                            localStorage.setItem('userLastName', result.data.lastName);
                        }

                        // Check if user is an admin and show admin section and card
                        if (result.data.role === 'admin') {
                            // Show admin section in sidebar
                            const adminSection = document.getElementById('adminSection');
                            if (adminSection) {
                                adminSection.style.display = 'block';
                            }

                            // Show admin dashboard card
                            const adminDashboardCard = document.getElementById('adminDashboardCard');
                            if (adminDashboardCard) {
                                adminDashboardCard.style.display = 'block';
                            }

                            // Show admin header button
                            const adminHeaderBtn = document.getElementById('adminHeaderBtn');
                            if (adminHeaderBtn) {
                                adminHeaderBtn.style.display = 'inline-flex';
                            }
                        }

                        // Update dashboard with personalized content if available
                        if (result.data.pregnancyDueDate) {
                            // If user has a due date, show pregnancy-related cards first
                            const dueDate = new Date(result.data.pregnancyDueDate);
                            const today = new Date();
                            const diffTime = dueDate - today;
                            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                            if (diffDays > 0) {
                                // Add a countdown card at the beginning of the dashboard
                                const dashboardGrid = document.querySelector('.dashboard-grid');
                                const countdownCard = document.createElement('div');
                                countdownCard.className = 'dashboard-card';
                                countdownCard.innerHTML = `
                                    <div class="dashboard-card-header">
                                        <div class="dashboard-card-icon">
                                            <i class="fas fa-hourglass-half"></i>
                                        </div>
                                        <h2 class="dashboard-card-title">Countdown to Due Date</h2>
                                    </div>
                                    <div class="dashboard-card-content">
                                        <p>Your baby is expected to arrive in <strong>${diffDays} days</strong>!</p>
                                        <p>Due date: ${dueDate.toLocaleDateString()}</p>
                                    </div>
                                    <div class="dashboard-card-footer">
                                        <a href="/tools#due-date" class="dashboard-card-link">Update Due Date <i class="fas fa-arrow-right"></i></a>
                                    </div>
                                `;
                                dashboardGrid.prepend(countdownCard);
                            }
                        }
                    } else {
                        console.error('API call failed:', result.message);
                        // Token is invalid, redirect to login (using clean URL)
                        apiService.clearToken();
                        window.location.href = '/login';
                    }
                }
            } catch (error) {
                console.error('Error getting user data:', error);
                // If API call fails but we have local data, don't redirect
                if (!userFirstName) {
                    // Only redirect to login if we don't have any user data (using clean URL)
                    apiService.clearToken();
                    window.location.href = '/login';
                }
            }

            // Sidebar toggle functionality for mobile
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('dashboardSidebar');

            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');

                    // Change icon based on sidebar state
                    const icon = this.querySelector('i');
                    if (sidebar.classList.contains('active')) {
                        icon.className = 'fas fa-times';
                    } else {
                        icon.className = 'fas fa-bars';
                    }
                });

                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(event) {
                    const isClickInsideSidebar = sidebar.contains(event.target);
                    const isClickOnToggle = sidebarToggle.contains(event.target);

                    if (!isClickInsideSidebar && !isClickOnToggle && sidebar.classList.contains('active')) {
                        sidebar.classList.remove('active');
                        sidebarToggle.querySelector('i').className = 'fas fa-bars';
                    }
                });
            }

            // Handle both logout buttons (sidebar and nav)
            const logoutButtons = document.querySelectorAll('.logout-btn');
            logoutButtons.forEach(button => {
                button.addEventListener('click', async function(e) {
                    e.preventDefault();

                    // Show logout confirmation
                    if (confirm('Are you sure you want to log out?')) {
                        try {
                            // Show loading state
                            const originalHTML = this.innerHTML;
                            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging out...';
                            this.disabled = true;

                            // Call logout API using the API service
                            await apiService.logout();

                            // Show success message
                            alert('You have been successfully logged out.');

                            // Redirect to home using clean URL
                            window.location.href = '/';
                        } catch (error) {
                            console.error('Logout error:', error);
                            alert('An error occurred during logout. Please try again.');

                            // Reset button
                            this.innerHTML = originalHTML;
                            this.disabled = false;
                        }
                    }
                });
            });

            // Highlight active sidebar link based on current URL
            const currentPath = window.location.pathname;
            const currentHash = window.location.hash;

            const sidebarLinks = document.querySelectorAll('.sidebar-menu-link');
            sidebarLinks.forEach(link => {
                const linkPath = link.getAttribute('href');

                // Check if the link matches the current path or hash
                if (linkPath === currentPath ||
                    (currentHash && linkPath.includes(currentHash)) ||
                    (currentPath === '/' && linkPath === '/dashboard')) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>
