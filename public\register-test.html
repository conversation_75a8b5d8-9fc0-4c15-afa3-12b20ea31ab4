<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Register Test</h1>
    
    <div>
        <button id="testDirectApi">Test Direct API Call</button>
        <button id="testRegisterUser">Test registerUser Function</button>
    </div>
    
    <div id="result">Results will appear here...</div>
    
    <script>
        // Helper function to get auth header
        function getAuthHeader() {
            const token = localStorage.getItem('token');
            return token ? { 'Authorization': `Bearer ${token}` } : {};
        }

        // Register function
        async function registerUser(userData) {
            try {
                console.log('registerUser called with:', { ...userData, password: '***' });
                
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });
                
                console.log('Register API response status:', response.status);
                
                const data = await response.json();
                console.log('Register API response data:', data);
                
                return data;
            } catch (error) {
                console.error('Registration error:', error);
                return { success: false, message: 'An error occurred during registration. Please try again.' };
            }
        }

        // Test direct API call
        document.getElementById('testDirectApi').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing direct API call...</p>';
            
            try {
                const userData = {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                    password: 'password123'
                };
                
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML += `
                    <h3>Direct API Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                if (data.success) {
                    resultDiv.innerHTML += `<p class="success">Direct API call successful!</p>`;
                } else {
                    resultDiv.innerHTML += `<p class="error">Direct API call failed: ${data.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `
                    <p class="error">Error in direct API call:</p>
                    <pre>${error.message}</pre>
                `;
            }
        });
        
        // Test registerUser function
        document.getElementById('testRegisterUser').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing registerUser function...</p>';
            
            try {
                const userData = {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                    password: 'password123'
                };
                
                const result = await registerUser(userData);
                
                resultDiv.innerHTML += `
                    <h3>registerUser Function Response:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
                if (result.success) {
                    resultDiv.innerHTML += `<p class="success">registerUser function call successful!</p>`;
                    
                    // Store token in localStorage
                    if (result.token) {
                        localStorage.setItem('token', result.token);
                        resultDiv.innerHTML += `<p>Token stored in localStorage</p>`;
                    }
                } else {
                    resultDiv.innerHTML += `<p class="error">registerUser function call failed: ${result.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `
                    <p class="error">Error in registerUser function:</p>
                    <pre>${error.message}</pre>
                `;
            }
        });
    </script>
</body>
</html>
