/**
 * Admin Dashboard functionality for MATERNIFY
 * This script handles the admin dashboard features including user management, content management,
 * analytics, settings, and system logs
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin Dashboard script loaded');

    // Check if user is logged in and has admin privileges
    checkAdminAccess();

    // DOM Elements
    const adminTabs = document.querySelectorAll('.admin-tab');
    const tabContents = document.querySelectorAll('.admin-tab-content');
    const sidebarLinks = document.querySelectorAll('.sidebar-menu-link');
    const refreshDataBtn = document.getElementById('refreshDataBtn');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('dashboardSidebar');

    // Initialize the dashboard
    initializeDashboard();

    // Tab switching functionality
    adminTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            switchTab(tabId);
        });
    });

    // Sidebar link functionality
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const tabId = this.getAttribute('data-tab');
            if (tabId) {
                e.preventDefault();
                switchTab(tabId);
            }
        });
    });

    // Refresh data button
    if (refreshDataBtn) {
        refreshDataBtn.addEventListener('click', function(e) {
            e.preventDefault();
            refreshDashboardData();
        });
    }

    // Sidebar toggle for mobile
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');

            // Change icon based on sidebar state
            const icon = this.querySelector('i');
            if (sidebar.classList.contains('active')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });
    }

    // Footer tab links
    const footerTabLinks = document.querySelectorAll('.footer-tools a[data-tab]');
    footerTabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabId = this.getAttribute('data-tab');
            switchTab(tabId);
        });
    });

    /**
     * Check if the current user has admin access
     */
    function checkAdminAccess() {
        // In a real application, this would check with the server
        // For now, we'll simulate admin access
        const user = JSON.parse(localStorage.getItem('user')) || {};
        const isAdmin = user.role === 'admin';

        if (!isAdmin) {
            // For demo purposes, we'll just set the user as admin
            // In a real app, you would redirect to login or show access denied
            console.log('Setting demo admin user');
            const adminUser = {
                firstName: 'Admin',
                lastName: 'User',
                email: '<EMAIL>',
                role: 'admin'
            };

            localStorage.setItem('user', JSON.stringify(adminUser));
            localStorage.setItem('userFirstName', adminUser.firstName);
            localStorage.setItem('userLastName', adminUser.lastName);

            // Update UI with admin user
            updateUserDisplay(adminUser);
        } else {
            updateUserDisplay(user);
        }
    }

    /**
     * Update the user display in the UI
     */
    function updateUserDisplay(user) {
        const userNameElements = document.querySelectorAll('#user-name, #sidebar-user-name, #nav-user-name');
        userNameElements.forEach(element => {
            if (element) {
                element.textContent = user.firstName || 'Admin';
            }
        });

        const userInitial = document.getElementById('user-initial');
        if (userInitial) {
            userInitial.textContent = (user.firstName || 'A').charAt(0).toUpperCase();
        }
    }

    /**
     * Initialize the dashboard with data
     */
    async function initializeDashboard() {
        try {
            // Load real data from API
            await loadDashboardData();
        } catch (error) {
            console.error('Error initializing dashboard:', error);
            // Fallback to mock data if API fails
            loadMockData();
        }

        // Load tab content
        loadTabContent('users');
        loadTabContent('content');
        loadTabContent('analytics');
        loadTabContent('settings');
        loadTabContent('logs');
    }

    /**
     * Switch between tabs
     */
    function switchTab(tabId) {
        // Update tab buttons
        adminTabs.forEach(tab => {
            if (tab.getAttribute('data-tab') === tabId) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });

        // Update sidebar links
        sidebarLinks.forEach(link => {
            if (link.getAttribute('data-tab') === tabId) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });

        // Update tab content
        tabContents.forEach(content => {
            if (content.id === `${tabId}-tab`) {
                content.classList.add('active');
            } else {
                content.classList.remove('active');
            }
        });

        // Close mobile sidebar after tab switch
        if (window.innerWidth <= 992 && sidebar.classList.contains('active')) {
            sidebar.classList.remove('active');
            if (sidebarToggle) {
                sidebarToggle.querySelector('i').className = 'fas fa-bars';
            }
        }
    }

    /**
     * Load data from the API
     */
    async function loadDashboardData() {
        try {
            // Show loading state
            document.getElementById('totalUsers').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            document.getElementById('newUsers').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            document.getElementById('totalContent').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            document.getElementById('totalViews').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Get dashboard stats from API
            const result = await adminApiService.getDashboardStats();

            if (result.success) {
                const { data } = result;

                // Update statistics
                document.getElementById('totalUsers').textContent = data.totalUsers.toLocaleString();
                document.getElementById('newUsers').textContent = data.newUsers.toLocaleString();
                document.getElementById('totalContent').textContent = data.totalContent.toLocaleString();
                document.getElementById('totalViews').textContent = data.totalViews.toLocaleString();

                // Load recent activities
                const activityTableBody = document.getElementById('activity-table-body');
                if (activityTableBody && data.recentActivities) {
                    activityTableBody.innerHTML = '';

                    data.recentActivities.forEach(activity => {
                        const row = document.createElement('tr');
                        const date = new Date(activity.timestamp).toLocaleString();
                        const user = activity.user ? activity.user.email : 'System';

                        row.innerHTML = `
                            <td>${date}</td>
                            <td>${user}</td>
                            <td>${formatActivityAction(activity.action)}</td>
                            <td>${activity.details || ''}</td>
                        `;
                        activityTableBody.appendChild(row);
                    });
                }

                // Load system alerts
                const alertsContainer = document.getElementById('alerts-container');
                if (alertsContainer && data.systemAlerts) {
                    alertsContainer.innerHTML = '';

                    if (data.systemAlerts.length === 0) {
                        alertsContainer.innerHTML = '<p>No system alerts at this time.</p>';
                    } else {
                        data.systemAlerts.forEach(alert => {
                            const alertElement = document.createElement('div');
                            alertElement.className = `alert alert-${alert.level}`;

                            let icon = 'info-circle';
                            if (alert.level === 'warning') icon = 'exclamation-triangle';
                            if (alert.level === 'error') icon = 'exclamation-circle';

                            const date = new Date(alert.timestamp).toLocaleString();

                            alertElement.innerHTML = `
                                <i class="fas fa-${icon}"></i>
                                <div>
                                    <strong>${alert.level.toUpperCase()}</strong>: ${alert.message}
                                    <div class="alert-timestamp">${date}</div>
                                </div>
                            `;
                            alertsContainer.appendChild(alertElement);
                        });
                    }
                }

                // Create pagination for activity table
                const activityPagination = document.getElementById('activity-pagination');
                if (activityPagination) {
                    activityPagination.innerHTML = `
                        <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
                    `;
                }
            } else {
                console.error('Failed to load dashboard data:', result.message);
                showErrorMessage('Failed to load dashboard data. Please try again.');
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            showErrorMessage('Error loading dashboard data. Please try again.');

            // Fallback to mock data if API fails
            loadMockData();
        }
    }

    /**
     * Format activity action for display
     */
    function formatActivityAction(action) {
        const actionMap = {
            'login': 'Login',
            'logout': 'Logout',
            'registration': 'Registration',
            'password_reset': 'Password Reset',
            'content_create': 'Content Created',
            'content_update': 'Content Updated',
            'content_delete': 'Content Deleted',
            'user_update': 'User Updated',
            'user_delete': 'User Deleted',
            'settings_update': 'Settings Updated',
            'other': 'Other Activity'
        };

        return actionMap[action] || action;
    }

    /**
     * Show error message
     */
    function showErrorMessage(message) {
        const alertsContainer = document.getElementById('alerts-container');
        if (alertsContainer) {
            alertsContainer.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>${message}</span>
                </div>
            `;
        }
    }

    /**
     * Load mock data for fallback
     */
    function loadMockData() {
        // Update statistics
        document.getElementById('totalUsers').textContent = '1,245';
        document.getElementById('newUsers').textContent = '87';
        document.getElementById('totalContent').textContent = '328';
        document.getElementById('totalViews').textContent = '24.5K';

        // Load recent activity
        const activityData = [
            { date: '2025-05-21 14:32', user: '<EMAIL>', activity: 'Login', details: 'Successful login from IP ***********' },
            { date: '2025-05-21 13:15', user: '<EMAIL>', activity: 'Registration', details: 'New user registration' },
            { date: '2025-05-21 12:45', user: '<EMAIL>', activity: 'Content Update', details: 'Updated "Pregnancy Care" page' },
            { date: '2025-05-21 11:30', user: '<EMAIL>', activity: 'Login', details: 'Failed login attempt (3rd attempt)' },
            { date: '2025-05-21 10:20', user: '<EMAIL>', activity: 'Settings', details: 'Changed site notification settings' },
            { date: '2025-05-20 16:45', user: '<EMAIL>', activity: 'Registration', details: 'New user registration' },
            { date: '2025-05-20 15:30', user: '<EMAIL>', activity: 'Content Update', details: 'Added new video to resources' }
        ];

        const activityTableBody = document.getElementById('activity-table-body');
        if (activityTableBody) {
            activityTableBody.innerHTML = '';
            activityData.forEach(activity => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${activity.date}</td>
                    <td>${activity.user}</td>
                    <td>${activity.activity}</td>
                    <td>${activity.details}</td>
                `;
                activityTableBody.appendChild(row);
            });
        }

        // Load system alerts
        const alertsContainer = document.getElementById('alerts-container');
        if (alertsContainer) {
            alertsContainer.innerHTML = '';
            const alertsData = [
                { type: 'warning', message: 'Database backup scheduled for tonight at 2:00 AM. Expect brief service interruption.' },
                { type: 'error', message: 'Failed login attempts threshold reached <NAME_EMAIL>. Account temporarily locked.' },
                { type: 'info', message: 'System update completed successfully. New features are now available.' }
            ];

            alertsData.forEach(alert => {
                const alertElement = document.createElement('div');
                alertElement.className = `alert alert-${alert.type}`;

                let icon = 'info-circle';
                if (alert.type === 'warning') icon = 'exclamation-triangle';
                if (alert.type === 'error') icon = 'exclamation-circle';

                alertElement.innerHTML = `
                    <i class="fas fa-${icon}"></i>
                    <span>${alert.message}</span>
                `;
                alertsContainer.appendChild(alertElement);
            });
        }

        // Create pagination for activity table
        const activityPagination = document.getElementById('activity-pagination');
        if (activityPagination) {
            activityPagination.innerHTML = `
                <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                <button class="pagination-btn active">1</button>
                <button class="pagination-btn">2</button>
                <button class="pagination-btn">3</button>
                <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
            `;
        }
    }

    /**
     * Load content for specific tabs
     */
    function loadTabContent(tabId) {
        const tabContent = document.getElementById(`${tabId}-tab`);
        if (!tabContent) return;

        switch (tabId) {
            case 'users':
                loadUserManagementTab(tabContent);
                break;
            case 'content':
                loadContentManagementTab(tabContent);
                break;
            case 'analytics':
                loadAnalyticsTab(tabContent);
                break;
            case 'settings':
                loadSettingsTab(tabContent);
                break;
            case 'logs':
                loadLogsTab(tabContent);
                break;
        }
    }

    /**
     * Load User Management tab content
     */
    function loadUserManagementTab(tabContent) {
        tabContent.innerHTML = `
            <div class="admin-card">
                <h3 class="admin-card-title"><i class="fas fa-users"></i> User Management</h3>
                <div class="search-filter">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search users...">
                    </div>
                    <div class="filter-dropdown">
                        <select>
                            <option value="all">All Users</option>
                            <option value="active">Active Users</option>
                            <option value="inactive">Inactive Users</option>
                            <option value="admin">Administrators</option>
                        </select>
                    </div>
                    <button class="btn btn-primary"><i class="fas fa-user-plus"></i> Add New User</button>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Registered</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1001</td>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>User</td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td>2025-04-15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View User"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit User"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete User"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>Sarah Smith</td>
                            <td><EMAIL></td>
                            <td>User</td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td>2025-05-21</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View User"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit User"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete User"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>1003</td>
                            <td>Admin User</td>
                            <td><EMAIL></td>
                            <td>Administrator</td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td>2025-01-01</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View User"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit User"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete User" disabled><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>1004</td>
                            <td>Jane Wilson</td>
                            <td><EMAIL></td>
                            <td>User</td>
                            <td><span class="badge badge-danger">Locked</span></td>
                            <td>2025-03-10</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View User"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit User"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete User"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>1005</td>
                            <td>Michael Brown</td>
                            <td><EMAIL></td>
                            <td>User</td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td>2025-05-20</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View User"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit User"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete User"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="pagination">
                    <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        `;
    }

    /**
     * Load Content Management tab content
     */
    function loadContentManagementTab(tabContent) {
        tabContent.innerHTML = `
            <div class="admin-card">
                <h3 class="admin-card-title"><i class="fas fa-file-alt"></i> Content Management</h3>
                <div class="search-filter">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search content...">
                    </div>
                    <div class="filter-dropdown">
                        <select>
                            <option value="all">All Content</option>
                            <option value="pages">Pages</option>
                            <option value="articles">Articles</option>
                            <option value="videos">Videos</option>
                        </select>
                    </div>
                    <button class="btn btn-primary"><i class="fas fa-plus"></i> Add New Content</button>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Author</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>C001</td>
                            <td>Pregnancy Care Guidelines</td>
                            <td>Page</td>
                            <td>Admin</td>
                            <td><span class="badge badge-success">Published</span></td>
                            <td>2025-05-21</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View Content"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit Content"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete Content"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>C002</td>
                            <td>Infant Care Guide</td>
                            <td>Page</td>
                            <td>Admin</td>
                            <td><span class="badge badge-success">Published</span></td>
                            <td>2025-05-15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View Content"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit Content"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete Content"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>C003</td>
                            <td>Breastfeeding Techniques</td>
                            <td>Video</td>
                            <td>Admin</td>
                            <td><span class="badge badge-success">Published</span></td>
                            <td>2025-05-10</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View Content"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit Content"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete Content"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>C004</td>
                            <td>Nutrition During Pregnancy</td>
                            <td>Article</td>
                            <td>Dr. Smith</td>
                            <td><span class="badge badge-warning">Draft</span></td>
                            <td>2025-05-18</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View Content"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit Content"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete Content"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>C005</td>
                            <td>Baby Sleep Patterns</td>
                            <td>Article</td>
                            <td>Dr. Johnson</td>
                            <td><span class="badge badge-success">Published</span></td>
                            <td>2025-05-05</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn-icon btn-view" title="View Content"><i class="fas fa-eye"></i></button>
                                    <button class="btn-icon btn-edit" title="Edit Content"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon btn-delete" title="Delete Content"><i class="fas fa-trash"></i></button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="pagination">
                    <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        `;
    }

    /**
     * Load Analytics tab content
     */
    function loadAnalyticsTab(tabContent) {
        tabContent.innerHTML = `
            <div class="admin-card">
                <h3 class="admin-card-title"><i class="fas fa-chart-bar"></i> Analytics Overview</h3>
                <p>Analytics data will be displayed here with charts and graphs.</p>
                <div class="analytics-placeholder">
                    <p>Charts and graphs will be loaded here.</p>
                </div>
            </div>
        `;
    }

    /**
     * Load Settings tab content
     */
    function loadSettingsTab(tabContent) {
        tabContent.innerHTML = `
            <div class="admin-card">
                <h3 class="admin-card-title"><i class="fas fa-cog"></i> System Settings</h3>
                <form id="settingsForm">
                    <div class="settings-section">
                        <h4>General Settings</h4>
                        <div class="form-group">
                            <label for="siteName">Site Name</label>
                            <input type="text" id="siteName" value="MATERNIFY">
                        </div>
                        <div class="form-group">
                            <label for="siteDescription">Site Description</label>
                            <textarea id="siteDescription">Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</textarea>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h4>Email Settings</h4>
                        <div class="form-group">
                            <label for="adminEmail">Admin Email</label>
                            <input type="email" id="adminEmail" value="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="supportEmail">Support Email</label>
                            <input type="email" id="supportEmail" value="<EMAIL>">
                        </div>
                    </div>

                    <div class="settings-section">
                        <h4>Security Settings</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableTwoFactor" checked>
                                Enable Two-Factor Authentication for Admins
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableCaptcha" checked>
                                Enable CAPTCHA on Registration
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="maxLoginAttempts">Max Login Attempts</label>
                            <input type="number" id="maxLoginAttempts" value="5">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save Settings</button>
                        <button type="button" class="btn btn-secondary">Reset to Defaults</button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * Load System Logs tab content
     */
    function loadLogsTab(tabContent) {
        tabContent.innerHTML = `
            <div class="admin-card">
                <h3 class="admin-card-title"><i class="fas fa-list"></i> System Logs</h3>
                <div class="search-filter">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search logs...">
                    </div>
                    <div class="filter-dropdown">
                        <select>
                            <option value="all">All Logs</option>
                            <option value="error">Errors</option>
                            <option value="warning">Warnings</option>
                            <option value="info">Info</option>
                        </select>
                    </div>
                    <button class="btn btn-secondary"><i class="fas fa-download"></i> Export Logs</button>
                </div>
                <div class="logs-container">
                    <div class="log-entry log-info">
                        <span class="log-time">2025-05-21 14:32:15</span>
                        <span class="log-level">INFO</span>
                        <span class="log-message">User <EMAIL> logged in successfully</span>
                    </div>
                    <div class="log-entry log-warning">
                        <span class="log-time">2025-05-21 13:45:22</span>
                        <span class="log-level">WARNING</span>
                        <span class="log-message">Database connection pool reaching capacity (80%)</span>
                    </div>
                    <div class="log-entry log-error">
                        <span class="log-time">2025-05-21 12:30:10</span>
                        <span class="log-level">ERROR</span>
                        <span class="log-message">Failed to send email <NAME_EMAIL></span>
                    </div>
                    <div class="log-entry log-info">
                        <span class="log-time">2025-05-21 11:15:45</span>
                        <span class="log-level">INFO</span>
                        <span class="log-message">Content updated: Pregnancy Care Guidelines</span>
                    </div>
                    <div class="log-entry log-warning">
                        <span class="log-time">2025-05-21 10:05:33</span>
                        <span class="log-level">WARNING</span>
                        <span class="log-message">Multiple failed login attempts <NAME_EMAIL></span>
                    </div>
                </div>
                <div class="pagination">
                    <button class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn">2</button>
                    <button class="pagination-btn">3</button>
                    <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        `;
    }

    /**
     * Refresh dashboard data
     */
    async function refreshDashboardData() {
        // Show loading indicator
        const refreshBtn = document.getElementById('refreshDataBtn');
        if (refreshBtn) {
            const originalHTML = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            refreshBtn.disabled = true;

            try {
                // Load real data from API
                await loadDashboardData();

                // Reset button
                refreshBtn.innerHTML = originalHTML;
                refreshBtn.disabled = false;

                // Show success message
                const alertsContainer = document.getElementById('alerts-container');
                if (alertsContainer) {
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-info';
                    successAlert.innerHTML = `
                        <i class="fas fa-check-circle"></i>
                        <span>Dashboard data refreshed successfully!</span>
                    `;

                    // Insert at the beginning
                    alertsContainer.insertBefore(successAlert, alertsContainer.firstChild);

                    // Remove after 5 seconds
                    setTimeout(() => {
                        successAlert.remove();
                    }, 5000);
                }
            } catch (error) {
                console.error('Error refreshing dashboard data:', error);

                // Reset button
                refreshBtn.innerHTML = originalHTML;
                refreshBtn.disabled = false;

                // Show error message
                showErrorMessage('Error refreshing dashboard data. Please try again.');

                // Fallback to mock data
                loadMockData();
            }
        }
    }
});
