const SystemLog = require('../models/SystemLog');

// Get all system logs with filtering, sorting, and pagination (admin only)
exports.getAllLogs = async (req, res, next) => {
  try {
    // Implement pagination, filtering, and sorting
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 50;
    const skip = (page - 1) * limit;

    // Build query
    let query = SystemLog.find();

    // Filter by level
    if (req.query.level) {
      query = query.find({ level: req.query.level });
    }

    // Filter by date range
    if (req.query.startDate && req.query.endDate) {
      query = query.find({
        timestamp: {
          $gte: new Date(req.query.startDate),
          $lte: new Date(req.query.endDate),
        },
      });
    }

    // Search by message
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      query = query.find({
        $or: [
          { message: searchRegex },
          { source: searchRegex },
        ],
      });
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-timestamp');
    }

    // Execute query with pagination
    const logs = await query.skip(skip).limit(limit);

    // Get total count for pagination
    const total = await SystemLog.countDocuments(query.getFilter());

    res.status(200).json({
      success: true,
      count: logs.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
      data: logs,
    });
  } catch (error) {
    next(error);
  }
};

// Get log statistics (admin only)
exports.getLogStats = async (req, res, next) => {
  try {
    // Get log counts by level
    const levelStats = await SystemLog.aggregate([
      {
        $group: {
          _id: '$level',
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Get daily log counts for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyStats = await SystemLog.aggregate([
      {
        $match: {
          timestamp: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' },
          },
          count: { $sum: 1 },
          errorCount: {
            $sum: { $cond: [{ $eq: ['$level', 'error'] }, 1, 0] },
          },
          warningCount: {
            $sum: { $cond: [{ $eq: ['$level', 'warning'] }, 1, 0] },
          },
          infoCount: {
            $sum: { $cond: [{ $eq: ['$level', 'info'] }, 1, 0] },
          },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        levelStats,
        dailyStats,
      },
    });
  } catch (error) {
    next(error);
  }
};
