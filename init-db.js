const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const fs = require('fs');

// Load env vars
dotenv.config();

// Import models
const User = require('./models/User');
const Newsletter = require('./models/Newsletter');

// Sample data
const users = [
  {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    isEmailVerified: true
  },
  {
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    isEmailVerified: true
  }
];

const newsletters = [
  {
    email: '<EMAIL>',
    isSubscribed: true,
    topics: ['pregnancy', 'infant-care']
  },
  {
    email: '<EMAIL>',
    isSubscribed: true,
    topics: ['all']
  }
];

// Function to initialize database
const initializeDB = async () => {
  try {
    console.log('Initializing database...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log(`MongoDB Connected: ${mongoose.connection.host}`);
    
    // Clear existing data
    console.log('Clearing existing data...');
    await User.deleteMany({});
    await Newsletter.deleteMany({});
    
    // Create users
    console.log('Creating users...');
    for (const userData of users) {
      // Hash password
      const salt = await bcrypt.genSalt(10);
      userData.password = await bcrypt.hash(userData.password, salt);
      
      await User.create(userData);
    }
    
    // Create newsletter subscriptions
    console.log('Creating newsletter subscriptions...');
    for (const newsletterData of newsletters) {
      // Generate unsubscribe token
      const crypto = require('crypto');
      newsletterData.unsubscribeToken = crypto.randomBytes(20).toString('hex');
      
      await Newsletter.create(newsletterData);
    }
    
    console.log('Database initialization complete!');
    console.log('\nSample credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User: <EMAIL> / password123');
    
    // Close connection
    await mongoose.connection.close();
    console.log('Connection closed');
    
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
};

// Run the initialization
initializeDB()
  .then(success => {
    if (success) {
      console.log('Database initialization successful!');
    } else {
      console.log('Database initialization failed!');
    }
    process.exit(0);
  })
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
