/**
 * Logout functionality for MATERNIFY
 * This script handles the logout button functionality across all pages
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Logout script loaded');

    // Function to handle logout
    function handleLogout(e) {
        e.preventDefault();
        console.log('Logout button clicked');

        // Clear all authentication data from localStorage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('userFirstName');
        localStorage.removeItem('userLastName');
        localStorage.removeItem('pregnancyDueDate');
        localStorage.removeItem('babyBirthdate');
        localStorage.removeItem('userPreferences');

        // Show success message
        alert('You have been successfully logged out.');

        // Redirect to home page
        window.location.href = '/';
    }

    // Add event listeners to all logout buttons
    const logoutButtons = document.querySelectorAll('.logout-btn');
    logoutButtons.forEach(button => {
        console.log('Adding event listener to logout button');
        button.addEventListener('click', handleLogout);
    });

    // Also handle the sidebar logout button if it exists
    const sidebarLogoutBtn = document.getElementById('sidebarLogoutBtn');
    if (sidebarLogoutBtn) {
        console.log('Adding event listener to sidebar logout button');
        sidebarLogoutBtn.addEventListener('click', handleLogout);
    }

    // Update user name in the header and sidebar if available
    const userNameElements = document.querySelectorAll('#nav-user-name, #sidebar-user-name');
    const firstName = localStorage.getItem('userFirstName') || 'User';
    
    userNameElements.forEach(element => {
        if (element) {
            element.textContent = firstName;
        }
    });

    // Update user initial in sidebar avatar if available
    const userInitialElement = document.getElementById('user-initial');
    if (userInitialElement) {
        userInitialElement.textContent = firstName.charAt(0).toUpperCase();
    }
});
