<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Resources | MATERNIFY</title>
    <meta name="description" content="Educational videos on pregnancy, childbirth, infant care, and parenting to support your journey from pregnancy to parenthood.">
    <link rel="stylesheet" href="../css/styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        /* Custom styles for header to match footer */
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        .user-menu {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        .user-greeting {
            color: var(--white) !important;
        }
        .logout-btn {
            color: var(--white) !important;
        }
        .logout-btn:hover {
            color: var(--primary-color) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }

        /* Page-specific styles */
        .video-category-tabs {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-bottom: var(--spacing-xl);
        }

        .tab-button {
            padding: var(--spacing-sm) var(--spacing-lg);
            background-color: var(--light-bg);
            border: none;
            border-radius: var(--border-radius-md);
            font-family: var(--heading-font);
            font-weight: 500;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background-color: var(--accent-color);
        }

        .tab-button.active {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .video-card {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-md);
        }

        .video-thumbnail {
            position: relative;
            width: 100%;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
            background-color: var(--light-bg);
            overflow: hidden;
        }

        .video-thumbnail img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.05);
        }

        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }

        .video-card:hover .play-button {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .video-info {
            padding: var(--spacing-md);
        }

        .video-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
            color: var(--text-color);
        }

        .video-description {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-bottom: var(--spacing-sm);
        }

        .video-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: var(--text-light);
        }

        .video-duration {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .video-category {
            display: inline-block;
            padding: 2px 8px;
            background-color: var(--accent-color);
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .video-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-md);
        }

        .video-modal.active {
            display: flex;
        }

        .modal-content {
            position: relative;
            width: 100%;
            max-width: 900px;
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            cursor: pointer;
            z-index: 10;
            transition: background-color 0.3s ease;
        }

        .modal-close:hover {
            background-color: var(--primary-color);
        }

        .video-container {
            position: relative;
            width: 100%;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
        }

        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        .modal-info {
            padding: var(--spacing-md);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
            color: var(--text-color);
        }

        .modal-description {
            color: var(--text-light);
            margin-bottom: var(--spacing-md);
        }

        .modal-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: var(--text-light);
            margin-bottom: var(--spacing-md);
        }

        .resource-links {
            margin-top: var(--spacing-md);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-color);
        }

        .resource-links h4 {
            margin-bottom: var(--spacing-sm);
            font-size: 1rem;
        }

        .resource-links ul {
            list-style-type: none;
        }

        .resource-links li {
            margin-bottom: var(--spacing-xs);
        }

        .resource-links a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .resource-links a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .video-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }

            .video-category-tabs {
                flex-direction: column;
                align-items: center;
            }

            .tab-button {
                width: 100%;
                max-width: 300px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li>
                        <div class="user-menu">
                            <span class="user-greeting">Hello, <span id="nav-user-name">User</span>!</span>
                            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Video Resources</h1>
            <p>Educational videos to support your journey from pregnancy to parenthood</p>
        </div>
    </section>

    <section class="video-resources">
        <div class="container">
            <div class="video-category-tabs">
                <button class="tab-button active" data-tab="all-videos">All Videos</button>
                <button class="tab-button" data-tab="pregnancy-videos">Pregnancy</button>
                <button class="tab-button" data-tab="childbirth-videos">Childbirth</button>
                <button class="tab-button" data-tab="newborn-videos">Newborn Care</button>
                <button class="tab-button" data-tab="infant-videos">Infant Development</button>
                <button class="tab-button" data-tab="breastfeeding-videos">Breastfeeding</button>
                <button class="tab-button" data-tab="wellness-videos">Maternal Wellness</button>
            </div>

            <!-- All Videos Tab -->
            <div id="all-videos" class="tab-content active">
                <div class="video-grid">
                    <!-- Video cards will be dynamically generated here -->
                </div>
            </div>

            <!-- Pregnancy Videos Tab -->
            <div id="pregnancy-videos" class="tab-content">
                <div class="video-grid">
                    <!-- Pregnancy video cards will be dynamically generated here -->
                </div>
            </div>

            <!-- Childbirth Videos Tab -->
            <div id="childbirth-videos" class="tab-content">
                <div class="video-grid">
                    <!-- Childbirth video cards will be dynamically generated here -->
                </div>
            </div>

            <!-- Newborn Care Videos Tab -->
            <div id="newborn-videos" class="tab-content">
                <div class="video-grid">
                    <!-- Newborn care video cards will be dynamically generated here -->
                </div>
            </div>

            <!-- Infant Development Videos Tab -->
            <div id="infant-videos" class="tab-content">
                <div class="video-grid">
                    <!-- Infant development video cards will be dynamically generated here -->
                </div>
            </div>

            <!-- Breastfeeding Videos Tab -->
            <div id="breastfeeding-videos" class="tab-content">
                <div class="video-grid">
                    <!-- Breastfeeding video cards will be dynamically generated here -->
                </div>
            </div>

            <!-- Maternal Wellness Videos Tab -->
            <div id="wellness-videos" class="tab-content">
                <div class="video-grid">
                    <!-- Maternal wellness video cards will be dynamically generated here -->
                </div>
            </div>
        </div>
    </section>

    <!-- Video Modal -->
    <div id="videoModal" class="video-modal">
        <div class="modal-content">
            <div class="modal-close" id="modalClose">
                <i class="fas fa-times"></i>
            </div>
            <div class="video-container">
                <iframe id="videoFrame" src="" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
            <div class="modal-info">
                <h2 class="modal-title" id="modalTitle"></h2>
                <div class="modal-meta">
                    <span class="modal-category" id="modalCategory"></span>
                    <span class="modal-duration" id="modalDuration"></span>
                </div>
                <p class="modal-description" id="modalDescription"></p>
                <div class="resource-links">
                    <h4>Related Resources</h4>
                    <ul id="modalResources">
                        <!-- Related resource links will be dynamically generated here -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pages/pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="/pages/infant-care.html">Infant Care</a></li>
                        <li><a href="/pages/tools.html">Tools</a></li>
                        <li><a href="/pages/community.html">Community</a></li>
                        <li><a href="/pages/blog.html">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/pages/tools.html#due-date">Due Date Calculator</a></li>
                        <li><a href="/pages/tools.html#bmi">BMI Tracker</a></li>
                        <li><a href="/pages/tools.html#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/pages/tools.html#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <script src="../js/logout.js"></script>
    <script src="../js/video-resources.js"></script>
