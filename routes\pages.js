const express = require('express');
const path = require('path');
const router = express.Router();
const { protect } = require('../middleware/auth');
const fs = require('fs');

// Define a helper function to send HTML files with proper headers
const sendHtmlFile = (res, filePath) => {
  // Set headers to prevent caching issues
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.setHeader('Content-Type', 'text/html');

  // Send the file
  res.sendFile(filePath, (err) => {
    if (err) {
      console.error(`Error sending file ${filePath}:`, err);
      res.status(err.status || 500).send('Error loading page');
    }
  });
};

// Home page
router.get('/', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'index.html'));
});

// Infant care page - ensure it works with clean URL
router.get('/infant-care', (req, res) => {
  console.log('Serving infant-care page with clean URL');
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'infant-care.html'));
});

// Handle any requests that might include .html extension
router.get('/infant-care.html', (req, res) => {
  console.log('Redirecting from infant-care.html to clean URL');
  res.redirect(301, '/infant-care');
});

// Pregnancy care page
router.get('/pregnancy-care', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'pregnancy-care.html'));
});

// Tools page
router.get('/tools', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'tools.html'));
});

// Community page
router.get('/community', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'community.html'));
});

// Blog/articles page
router.get('/blog', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'blog.html'));
});

// Login page - ensure it works with clean URL
router.get('/login', (req, res) => {
  console.log('Serving login page with clean URL');
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'login.html'));
});

// Handle any requests that might include .html extension
router.get('/login.html', (req, res) => {
  console.log('Redirecting from login.html to clean URL');
  res.redirect(301, '/login');
});

// Register page
router.get('/register', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'register.html'));
});

// Dashboard page (protected route) - ensure it works with clean URL
router.get('/dashboard', (req, res) => {
  console.log('Serving dashboard page with clean URL');
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'dashboard.html'));
});

// Handle any requests that might include .html extension
router.get('/dashboard.html', (req, res) => {
  console.log('Redirecting from dashboard.html to clean URL');
  res.redirect(301, '/dashboard');
});

// Forgot password page
router.get('/forgot-password', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'forgot-password.html'));
});

// Reset password page
router.get('/reset-password', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'reset-password.html'));
});

// Profile page
router.get('/profile', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'profile.html'));
});

// Health journal page
router.get('/health-journal', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'health-journal.html'));
});

// Video resources page
router.get('/video-resources', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'video-resources.html'));
});

// Admin dashboard page
router.get('/admin-dashboard', (req, res) => {
  sendHtmlFile(res, path.join(__dirname, '..', 'public', 'pages', 'admin-dashboard.html'));
});

// Handle redirects for pages that might be accessed with .html extension
router.get('/profile.html', (req, res) => {
  res.redirect(301, '/profile');
});

router.get('/health-journal.html', (req, res) => {
  res.redirect(301, '/health-journal');
});

router.get('/video-resources.html', (req, res) => {
  res.redirect(301, '/video-resources');
});

router.get('/admin-dashboard.html', (req, res) => {
  res.redirect(301, '/admin-dashboard');
});

router.get('/pregnancy-care.html', (req, res) => {
  res.redirect(301, '/pregnancy-care');
});

router.get('/tools.html', (req, res) => {
  res.redirect(301, '/tools');
});

router.get('/community.html', (req, res) => {
  res.redirect(301, '/community');
});

router.get('/blog.html', (req, res) => {
  res.redirect(301, '/blog');
});

router.get('/register.html', (req, res) => {
  res.redirect(301, '/register');
});

router.get('/forgot-password.html', (req, res) => {
  res.redirect(301, '/forgot-password');
});

router.get('/reset-password.html', (req, res) => {
  res.redirect(301, '/reset-password');
});

// Catch-all route for any other page requests
// This ensures that all navigation links work on the first click
router.get('*', (req, res, next) => {
  console.log(`Catch-all route handling: ${req.path}`);

  // Skip API routes and static file extensions
  if (req.path.startsWith('/api') ||
      req.path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i)) {
    return next();
  }

  // If the URL ends with .html, redirect to the clean URL version
  if (req.path.endsWith('.html')) {
    const cleanPath = req.path.substring(0, req.path.length - 5);
    console.log(`Redirecting from ${req.path} to clean URL: ${cleanPath}`);
    return res.redirect(301, cleanPath);
  }

  // Check if the requested path exists as a file in the public directory
  const filePath = path.join(__dirname, '..', 'public', req.path);

  try {
    // If the file exists and is a file (not a directory), serve it
    if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
      console.log(`Serving file: ${filePath}`);
      return sendHtmlFile(res, filePath);
    }

    // If the path is a known route but doesn't have a direct file, try to serve the corresponding HTML
    const htmlPath = path.join(__dirname, '..', 'public', 'pages', `${req.path.substring(1)}.html`);
    if (fs.existsSync(htmlPath)) {
      console.log(`Serving HTML file: ${htmlPath}`);
      return sendHtmlFile(res, htmlPath);
    }

    // If the path is the root of a section, try to serve index.html in that directory
    const indexPath = path.join(filePath, 'index.html');
    if (fs.existsSync(indexPath) && fs.statSync(indexPath).isFile()) {
      console.log(`Serving index file: ${indexPath}`);
      return sendHtmlFile(res, indexPath);
    }

    // If we get here, let the next middleware handle it (404 handler)
    console.log(`No matching file found for: ${req.path}`);
    next();
  } catch (err) {
    console.error(`Error in catch-all route for ${req.path}:`, err);
    next();
  }
});

module.exports = router;
