// Authentication controller for MATERNIFY
const User = require('../models/User');
const Newsletter = require('../models/Newsletter');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const fs = require('fs');

// Function to check if MongoDB is available
const isMongoDBAvailable = () => {
  try {
    // Check mongoose connection state
    // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting
    return User.db.readyState === 1;
  } catch (error) {
    fs.appendFileSync('server-log.txt', `Auth controller error checking MongoDB: ${error.message}\n`);
    return false;
  }
};

// Log MongoDB connection status on startup
try {
  if (!isMongoDBAvailable()) {
    fs.appendFileSync('server-log.txt', 'Auth controller: MongoDB is not connected. Using fallback mode.\n');
  }
} catch (error) {
  fs.appendFileSync('server-log.txt', `Auth controller error: ${error.message}\n`);
}

// Helper function to send token response
const sendTokenResponse = (user, statusCode, res) => {
  // Create access token
  const token = user.getSignedJwtToken ? user.getSignedJwtToken() :
    jwt.sign({ id: user._id }, process.env.JWT_SECRET || 'maternify_secret_key', {
      expiresIn: process.env.JWT_EXPIRE || '30d'
    });

  // Create refresh token
  const refreshToken = jwt.sign(
    { id: user._id },
    process.env.JWT_REFRESH_SECRET || 'maternify_refresh_secret_key',
    { expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d' }
  );

  const options = {
    expires: new Date(
      Date.now() + (process.env.JWT_COOKIE_EXPIRE || 30) * 24 * 60 * 60 * 1000
    ),
    httpOnly: true
  };

  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  // Create a user object without the password
  const userResponse = {
    _id: user._id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    role: user.role
  };

  res
    .status(statusCode)
    .cookie('token', token, options)
    .json({
      success: true,
      token,
      refreshToken,
      user: userResponse
    });
};

// @desc    Register user
// @route   POST /api/register
// @access  Public
exports.register = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      phone,
      dueDate,
      babyBirthdate,
      password,
      preferences
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'All required fields must be provided'
      });
    }

    // Log registration attempt
    fs.appendFileSync('server-log.txt', `Registration attempt for email: ${email}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for registration\n');

      // Create a mock user for demonstration
      const mockUser = {
        _id: '123456789',
        firstName,
        lastName,
        email,
        phone: phone || null,
        role: 'user',
        preferences: preferences || {
          newsletter: false,
          smsUpdates: false
        }
      };

      // Generate a token
      const token = jwt.sign(
        { id: mockUser._id },
        process.env.JWT_SECRET || 'maternify_secret_key',
        { expiresIn: process.env.JWT_EXPIRE || '30d' }
      );

      fs.appendFileSync('server-log.txt', `Mock registration successful for ${email} with phone ${phone || 'not provided'}\n`);

      return res.status(201).json({
        success: true,
        token,
        user: mockUser
      });
    }

    // MongoDB is available, proceed with normal registration
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      fs.appendFileSync('server-log.txt', `Registration failed: Email ${email} already registered\n`);
      return res.status(400).json({
        success: false,
        message: 'Email already registered'
      });
    }

    // Create user with additional fields
    const userData = {
      firstName,
      lastName,
      email,
      password,
      phoneNumber: phone || null
    };

    // Add optional fields if provided
    if (dueDate) {
      userData.pregnancyDueDate = new Date(dueDate);
    }

    if (babyBirthdate) {
      userData.childrenInfo = [{
        dateOfBirth: new Date(babyBirthdate)
      }];
    }

    const user = await User.create(userData);

    // Generate email verification token
    const verificationToken = user.getEmailVerificationToken();
    await user.save({ validateBeforeSave: false });

    // In a real application, you would send an email with the verification link
    // For now, we'll just log it
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/verify-email/${verificationToken}`;
    fs.appendFileSync('server-log.txt', `Email verification URL: ${verificationUrl}\n`);

    // If user subscribed to newsletter, add them to the newsletter list
    if (preferences && preferences.newsletter) {
      try {
        // In a real app, this would be handled by a separate service
        fs.appendFileSync('server-log.txt', `Adding ${email} to newsletter subscribers\n`);
      } catch (err) {
        fs.appendFileSync('server-log.txt', `Error adding to newsletter: ${err.message}\n`);
      }
    }

    // If user opted for SMS updates, log it (in a real app, you'd add them to SMS service)
    if (preferences && preferences.smsUpdates && phone) {
      fs.appendFileSync('server-log.txt', `User ${email} opted for SMS updates to ${phone}\n`);
    }

    fs.appendFileSync('server-log.txt', `Registration successful for email: ${email}\n`);

    // Send token response
    sendTokenResponse(user, 201, res);
  } catch (error) {
    console.error('Registration error:', error);
    fs.appendFileSync('server-log.txt', `Registration error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
};

// @desc    Login user
// @route   POST /api/login
// @access  Public
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate email and password
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }

    // Log login attempt
    fs.appendFileSync('server-log.txt', `Login attempt for email: ${email}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for login\n');

      // For demonstration purposes, accept any login with email and password
      // In a real app, you would have a more secure fallback

      // Create a mock user
      const mockUser = {
        _id: '123456789',
        firstName: 'Test',
        lastName: 'User',
        email,
        role: 'user'
      };

      // Generate an access token
      const token = jwt.sign(
        { id: mockUser._id },
        process.env.JWT_SECRET || 'maternify_secret_key',
        { expiresIn: process.env.JWT_EXPIRE || '30d' }
      );

      // Generate a refresh token
      const refreshToken = jwt.sign(
        { id: mockUser._id },
        process.env.JWT_REFRESH_SECRET || 'maternify_refresh_secret_key',
        { expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d' }
      );

      fs.appendFileSync('server-log.txt', 'Mock login successful\n');

      return res.status(200).json({
        success: true,
        token,
        refreshToken,
        user: mockUser
      });
    }

    // MongoDB is available, proceed with normal login
    // Check for user
    const user = await User.findOne({ email }).select('+password');
    if (!user) {
      fs.appendFileSync('server-log.txt', `Login failed: User with email ${email} not found\n`);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      fs.appendFileSync('server-log.txt', `Login failed: Invalid password for email ${email}\n`);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login time
    user.lastLogin = Date.now();
    await user.save({ validateBeforeSave: false });

    fs.appendFileSync('server-log.txt', `Login successful for email: ${email}\n`);

    // Send token response
    sendTokenResponse(user, 200, res);
  } catch (error) {
    console.error('Login error:', error);
    fs.appendFileSync('server-log.txt', `Login error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during login'
    });
  }
};

// @desc    Get current logged in user
// @route   GET /api/me
// @access  Private
exports.getMe = async (req, res) => {
  try {
    // Log get user attempt
    fs.appendFileSync('server-log.txt', 'Get current user attempt\n');

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for get current user\n');

      // For demonstration purposes, return a mock user
      // In a real app, you would verify the token and extract user info
      const mockUser = {
        _id: req.user ? req.user.id : '123456789',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'user'
      };

      fs.appendFileSync('server-log.txt', 'Mock get user successful\n');

      return res.status(200).json({
        success: true,
        data: mockUser
      });
    }

    // MongoDB is available, proceed with normal get user
    const user = await User.findById(req.user.id);

    if (!user) {
      fs.appendFileSync('server-log.txt', `Get user failed: User with id ${req.user.id} not found\n`);
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    fs.appendFileSync('server-log.txt', `Get user successful for id: ${req.user.id}\n`);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    fs.appendFileSync('server-log.txt', `Get user error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Log user out / clear cookie
// @route   GET/POST /api/logout
// @access  Public
exports.logout = (req, res) => {
  try {
    // Log logout attempt
    fs.appendFileSync('server-log.txt', `Logout attempt from ${req.method} request\n`);

    // Clear the access token cookie
    res.cookie('token', 'none', {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true
    });

    // Clear the refresh token cookie if it exists
    res.cookie('refreshToken', 'none', {
      expires: new Date(Date.now() + 10 * 1000),
      httpOnly: true
    });

    // Log the user ID if available
    if (req.user) {
      fs.appendFileSync('server-log.txt', `Logout successful for user ID: ${req.user.id}\n`);
    } else {
      fs.appendFileSync('server-log.txt', 'Logout successful (no user ID available)\n');
    }

    res.status(200).json({
      success: true,
      message: 'User logged out successfully'
    });
  } catch (error) {
    // Log the error
    console.error('Logout error:', error);
    fs.appendFileSync('server-log.txt', `Logout error: ${error.message}\n`);

    // Still try to clear cookies even if there was an error
    try {
      res.cookie('token', 'none', {
        expires: new Date(Date.now() + 10 * 1000),
        httpOnly: true
      });

      res.cookie('refreshToken', 'none', {
        expires: new Date(Date.now() + 10 * 1000),
        httpOnly: true
      });
    } catch (cookieError) {
      console.error('Error clearing cookies:', cookieError);
    }

    // Return success anyway to ensure the client proceeds with local logout
    res.status(200).json({
      success: true,
      message: 'Logout processed with warnings'
    });
  }
};

// @desc    Refresh JWT token
// @route   POST /api/refresh-token
// @access  Public
exports.refreshToken = async (req, res) => {
  try {
    // Get refresh token from request body
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
    }

    // Log refresh token attempt
    fs.appendFileSync('server-log.txt', 'Token refresh attempt\n');

    // Verify the refresh token
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || 'maternify_refresh_secret_key');
    } catch (error) {
      fs.appendFileSync('server-log.txt', `Invalid refresh token: ${error.message}\n`);
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token'
      });
    }

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for token refresh\n');

      // Create a mock user
      const mockUser = {
        _id: decoded.id || '123456789',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'user'
      };

      // Generate a new access token
      const accessToken = jwt.sign(
        { id: mockUser._id },
        process.env.JWT_SECRET || 'maternify_secret_key',
        { expiresIn: process.env.JWT_EXPIRE || '30d' }
      );

      // Generate a new refresh token
      const newRefreshToken = jwt.sign(
        { id: mockUser._id },
        process.env.JWT_REFRESH_SECRET || 'maternify_refresh_secret_key',
        { expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d' }
      );

      fs.appendFileSync('server-log.txt', 'Mock token refresh successful\n');

      return res.status(200).json({
        success: true,
        accessToken,
        refreshToken: newRefreshToken,
        user: mockUser
      });
    }

    // MongoDB is available, proceed with normal token refresh
    // Find user by ID from decoded token
    const user = await User.findById(decoded.id);

    if (!user) {
      fs.appendFileSync('server-log.txt', `Token refresh failed: User with id ${decoded.id} not found\n`);
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Generate new tokens
    const accessToken = jwt.sign(
      { id: user._id },
      process.env.JWT_SECRET || 'maternify_secret_key',
      { expiresIn: process.env.JWT_EXPIRE || '30d' }
    );

    const newRefreshToken = jwt.sign(
      { id: user._id },
      process.env.JWT_REFRESH_SECRET || 'maternify_refresh_secret_key',
      { expiresIn: process.env.JWT_REFRESH_EXPIRE || '7d' }
    );

    fs.appendFileSync('server-log.txt', `Token refresh successful for user id: ${user._id}\n`);

    // Return new tokens
    res.status(200).json({
      success: true,
      accessToken,
      refreshToken: newRefreshToken,
      user: {
        _id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    fs.appendFileSync('server-log.txt', `Token refresh error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during token refresh'
    });
  }
};

// Newsletter subscription controller
exports.subscribe = async (req, res) => {
  try {
    const { email, topics } = req.body;

    // Validate email
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Log subscription attempt
    fs.appendFileSync('server-log.txt', `Newsletter subscription attempt for email: ${email}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for newsletter subscription\n');

      fs.appendFileSync('server-log.txt', 'Mock newsletter subscription successful\n');

      return res.status(200).json({
        success: true,
        message: 'Subscription successful'
      });
    }

    // Check if email already exists in newsletter subscriptions
    let subscription = await Newsletter.findOne({ email });

    if (subscription) {
      // If already subscribed, update the subscription
      if (subscription.isSubscribed) {
        return res.status(400).json({
          success: false,
          message: 'Email is already subscribed to the newsletter'
        });
      } else {
        // If previously unsubscribed, resubscribe
        subscription.isSubscribed = true;
        subscription.subscribedAt = Date.now();
        subscription.unsubscribedAt = null;

        // Update topics if provided
        if (topics && Array.isArray(topics)) {
          subscription.topics = topics;
        }

        await subscription.save();

        fs.appendFileSync('server-log.txt', `Newsletter resubscription successful for email: ${email}\n`);

        return res.status(200).json({
          success: true,
          message: 'Resubscription successful'
        });
      }
    }

    // Generate unsubscribe token
    const unsubscribeToken = crypto.randomBytes(20).toString('hex');

    // Create new subscription
    subscription = await Newsletter.create({
      email,
      topics: topics && Array.isArray(topics) ? topics : ['all'],
      unsubscribeToken
    });

    fs.appendFileSync('server-log.txt', `Newsletter subscription successful for email: ${email}\n`);

    res.status(201).json({
      success: true,
      message: 'Subscription successful'
    });
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    fs.appendFileSync('server-log.txt', `Newsletter subscription error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during subscription'
    });
  }
};

// Forgot password controller
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Validate email
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Log forgot password attempt
    fs.appendFileSync('server-log.txt', `Forgot password attempt for email: ${email}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for forgot password\n');

      fs.appendFileSync('server-log.txt', 'Mock forgot password email sent\n');

      return res.status(200).json({
        success: true,
        message: 'Password reset email sent'
      });
    }

    // Find user by email
    const user = await User.findOne({ email });

    if (!user) {
      fs.appendFileSync('server-log.txt', `Forgot password failed: User with email ${email} not found\n`);
      return res.status(404).json({
        success: false,
        message: 'No user found with that email'
      });
    }

    // Generate reset token
    const resetToken = user.getResetPasswordToken();
    await user.save({ validateBeforeSave: false });

    // In a real application, you would send an email with the reset link
    // For now, we'll just log it
    const resetUrl = `${req.protocol}://${req.get('host')}/api/reset-password/${resetToken}`;
    fs.appendFileSync('server-log.txt', `Password reset URL: ${resetUrl}\n`);

    fs.appendFileSync('server-log.txt', `Forgot password email sent to: ${email}\n`);

    res.status(200).json({
      success: true,
      message: 'Password reset email sent'
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    fs.appendFileSync('server-log.txt', `Forgot password error: ${error.message}\n`);

    // If there was an error, clear the reset token fields
    if (isMongoDBAvailable()) {
      try {
        const user = await User.findOne({ email: req.body.email });
        if (user) {
          user.resetPasswordToken = undefined;
          user.resetPasswordExpire = undefined;
          await user.save({ validateBeforeSave: false });
        }
      } catch (err) {
        console.error('Error clearing reset token:', err);
      }
    }

    res.status(500).json({
      success: false,
      message: 'Server error during password reset request'
    });
  }
};

// Reset password controller
exports.resetPassword = async (req, res) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    // Validate password
    if (!password) {
      return res.status(400).json({
        success: false,
        message: 'Password is required'
      });
    }

    // Log reset password attempt
    fs.appendFileSync('server-log.txt', `Reset password attempt with token: ${token}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for reset password\n');

      fs.appendFileSync('server-log.txt', 'Mock password reset successful\n');

      return res.status(200).json({
        success: true,
        message: 'Password reset successful'
      });
    }

    // Hash token
    const resetPasswordToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    // Find user by token
    const user = await User.findOne({
      resetPasswordToken,
      resetPasswordExpire: { $gt: Date.now() }
    });

    if (!user) {
      fs.appendFileSync('server-log.txt', `Reset password failed: Invalid or expired token ${token}\n`);
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    // Update user
    user.password = password;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save();

    fs.appendFileSync('server-log.txt', `Reset password successful for email: ${user.email}\n`);

    // Send token response
    sendTokenResponse(user, 200, res);
  } catch (error) {
    console.error('Reset password error:', error);
    fs.appendFileSync('server-log.txt', `Reset password error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during password reset'
    });
  }
};

// Email verification controller
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;

    // Log verification attempt
    fs.appendFileSync('server-log.txt', `Email verification attempt with token: ${token}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for email verification\n');

      fs.appendFileSync('server-log.txt', 'Mock email verification successful\n');

      return res.status(200).json({
        success: true,
        message: 'Email verified successfully'
      });
    }

    // Hash token
    const emailVerificationToken = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    // Find user by token
    const user = await User.findOne({
      emailVerificationToken,
      emailVerificationExpire: { $gt: Date.now() }
    });

    if (!user) {
      fs.appendFileSync('server-log.txt', `Email verification failed: Invalid or expired token ${token}\n`);
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    // Update user
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpire = undefined;
    await user.save();

    fs.appendFileSync('server-log.txt', `Email verification successful for email: ${user.email}\n`);

    res.status(200).json({
      success: true,
      message: 'Email verified successfully'
    });
  } catch (error) {
    console.error('Email verification error:', error);
    fs.appendFileSync('server-log.txt', `Email verification error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during email verification'
    });
  }
};

// Newsletter unsubscribe controller
exports.unsubscribe = async (req, res) => {
  try {
    const { token } = req.params;

    // Validate token
    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Unsubscribe token is required'
      });
    }

    // Log unsubscribe attempt
    fs.appendFileSync('server-log.txt', `Newsletter unsubscribe attempt with token: ${token}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for newsletter unsubscribe\n');

      fs.appendFileSync('server-log.txt', 'Mock newsletter unsubscribe successful\n');

      return res.status(200).json({
        success: true,
        message: 'Unsubscription successful'
      });
    }

    // Find subscription by token
    const subscription = await Newsletter.findOne({ unsubscribeToken: token });

    if (!subscription) {
      fs.appendFileSync('server-log.txt', `Newsletter unsubscribe failed: Invalid token ${token}\n`);
      return res.status(400).json({
        success: false,
        message: 'Invalid unsubscribe token'
      });
    }

    // Update subscription
    subscription.isSubscribed = false;
    subscription.unsubscribedAt = Date.now();
    await subscription.save();

    fs.appendFileSync('server-log.txt', `Newsletter unsubscribe successful for email: ${subscription.email}\n`);

    res.status(200).json({
      success: true,
      message: 'Unsubscription successful'
    });
  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    fs.appendFileSync('server-log.txt', `Newsletter unsubscribe error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during unsubscription'
    });
  }
};
