const express = require('express');
const contentController = require('../controllers/contentController');
const { protect, restrictTo } = require('../middleware/auth');

const router = express.Router();

// Protect all routes
router.use(protect);

// Routes for both admin and regular users
router.get('/', contentController.getAllContent);
router.get('/:id', contentController.getContentById);

// Admin-only routes
router.post('/', restrictTo('admin'), contentController.createContent);
router.patch('/:id', restrictTo('admin'), contentController.updateContent);
router.delete('/:id', restrictTo('admin'), contentController.deleteContent);

module.exports = router;
