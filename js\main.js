document.addEventListener('DOMContentLoaded', function() {
    // Mobile Menu Toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenuBtn.classList.toggle('active');
            navMenu.classList.toggle('active');
            
            // Toggle hamburger menu animation
            const bars = mobileMenuBtn.querySelectorAll('.bar');
            if (mobileMenuBtn.classList.contains('active')) {
                bars[0].style.transform = 'rotate(-45deg) translate(-5px, 6px)';
                bars[1].style.opacity = '0';
                bars[2].style.transform = 'rotate(45deg) translate(-5px, -6px)';
            } else {
                bars[0].style.transform = 'none';
                bars[1].style.opacity = '1';
                bars[2].style.transform = 'none';
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navMenu.contains(event.target) && !mobileMenuBtn.contains(event.target) && navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                mobileMenuBtn.classList.remove('active');
                
                const bars = mobileMenuBtn.querySelectorAll('.bar');
                bars[0].style.transform = 'none';
                bars[1].style.opacity = '1';
                bars[2].style.transform = 'none';
            }
        });
    }
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            if (this.getAttribute('href') !== '#') {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    window.scrollTo({
                        top: target.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (navMenu && navMenu.classList.contains('active')) {
                        navMenu.classList.remove('active');
                        if (mobileMenuBtn) {
                            mobileMenuBtn.classList.remove('active');
                            const bars = mobileMenuBtn.querySelectorAll('.bar');
                            bars[0].style.transform = 'none';
                            bars[1].style.opacity = '1';
                            bars[2].style.transform = 'none';
                        }
                    }
                }
            }
        });
    });
    
    // Newsletter form submission (placeholder)
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const emailInput = this.querySelector('input[type="email"]');
            if (emailInput && emailInput.value) {
                // In a real application, you would send this to your backend
                alert('Thank you for subscribing to our newsletter!');
                emailInput.value = '';
            }
        });
    }
    
    // Add active class to current page in navigation
    const currentLocation = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentLocation || 
            (currentLocation.includes(link.getAttribute('href')) && link.getAttribute('href') !== 'index.html')) {
            link.classList.add('active');
        }
    });
    
    // Password visibility toggle
    const passwordToggles = document.querySelectorAll('.toggle-password');
    if (passwordToggles) {
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const passwordField = this.previousElementSibling;
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                this.innerHTML = type === 'password' ? '<i class="far fa-eye"></i>' : '<i class="far fa-eye-slash"></i>';
            });
        });
    }
    
    // Form validation
    const validateForm = (form, fields) => {
        let isValid = true;
        
        fields.forEach(field => {
            const input = form.querySelector(`#${field.id}`);
            const errorElement = form.querySelector(`#${field.id}Error`);
            
            if (!input || !errorElement) return;
            
            let fieldIsValid = true;
            let errorMessage = '';
            
            // Required field validation
            if (field.required && input.value.trim() === '') {
                fieldIsValid = false;
                errorMessage = field.requiredMessage || 'This field is required';
            }
            
            // Email validation
            if (field.type === 'email' && input.value.trim() !== '') {
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailPattern.test(input.value)) {
                    fieldIsValid = false;
                    errorMessage = field.typeMessage || 'Please enter a valid email address';
                }
            }
            
            // Password validation
            if (field.type === 'password' && input.value.trim() !== '') {
                const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/;
                if (!passwordPattern.test(input.value)) {
                    fieldIsValid = false;
                    errorMessage = field.typeMessage || 'Password must be at least 8 characters with letters and numbers';
                }
            }
            
            // Password confirmation validation
            if (field.type === 'passwordConfirm') {
                const passwordInput = form.querySelector(`#${field.matchWith}`);
                if (passwordInput && input.value !== passwordInput.value) {
                    fieldIsValid = false;
                    errorMessage = field.typeMessage || 'Passwords do not match';
                }
            }
            
            // Custom validation
            if (field.validate && typeof field.validate === 'function') {
                const customValidation = field.validate(input.value);
                if (customValidation !== true) {
                    fieldIsValid = false;
                    errorMessage = customValidation;
                }
            }
            
            // Update UI based on validation
            if (!fieldIsValid) {
                errorElement.textContent = errorMessage;
                errorElement.style.display = 'block';
                input.style.borderColor = 'var(--error-color)';
                isValid = false;
            } else {
                errorElement.style.display = 'none';
                input.style.borderColor = '';
            }
        });
        
        return isValid;
    };
    
    // Login form validation
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fields = [
                {
                    id: 'email',
                    required: true,
                    requiredMessage: 'Please enter your email address',
                    type: 'email',
                    typeMessage: 'Please enter a valid email address'
                },
                {
                    id: 'password',
                    required: true,
                    requiredMessage: 'Please enter your password'
                }
            ];
            
            if (validateForm(this, fields)) {
                // In a real application, this would authenticate with the server
                alert('Login successful! Redirecting to your dashboard.');
                window.location.href = '../index.html';
            }
        });
    }
    
    // Registration form validation
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fields = [
                {
                    id: 'firstName',
                    required: true,
                    requiredMessage: 'Please enter your first name'
                },
                {
                    id: 'lastName',
                    required: true,
                    requiredMessage: 'Please enter your last name'
                },
                {
                    id: 'email',
                    required: true,
                    requiredMessage: 'Please enter your email address',
                    type: 'email',
                    typeMessage: 'Please enter a valid email address'
                },
                {
                    id: 'password',
                    required: true,
                    requiredMessage: 'Please enter a password',
                    type: 'password',
                    typeMessage: 'Password must be at least 8 characters with letters and numbers'
                },
                {
                    id: 'confirmPassword',
                    required: true,
                    requiredMessage: 'Please confirm your password',
                    type: 'passwordConfirm',
                    matchWith: 'password',
                    typeMessage: 'Passwords do not match'
                }
            ];
            
            // Terms agreement validation
            const termsAgree = document.getElementById('termsAgree');
            let termsValid = true;
            
            if (termsAgree && !termsAgree.checked) {
                termsValid = false;
                termsAgree.nextElementSibling.style.color = 'var(--error-color)';
            } else if (termsAgree) {
                termsAgree.nextElementSibling.style.color = '';
            }
            
            if (validateForm(this, fields) && termsValid) {
                // In a real application, this would send data to the server
                alert('Registration successful! You can now log in.');
                window.location.href = 'login.html';
            }
        });
    }
    
    // Due Date Calculator
    const dueDateCalculator = document.getElementById('dueDateCalculator');
    if (dueDateCalculator) {
        dueDateCalculator.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const lmpDate = new Date(document.getElementById('lmpDate').value);
            const resultElement = document.getElementById('dueDateResult');
            
            if (isNaN(lmpDate.getTime())) {
                resultElement.innerHTML = '<div class="error-message">Please enter a valid date</div>';
                return;
            }
            
            // Calculate due date (LMP + 280 days)
            const dueDate = new Date(lmpDate);
            dueDate.setDate(lmpDate.getDate() + 280);
            
            // Format the date
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            const formattedDate = dueDate.toLocaleDateString('en-US', options);
            
            // Calculate current pregnancy week
            const today = new Date();
            const pregnancyDays = Math.floor((today - lmpDate) / (1000 * 60 * 60 * 24));
            const pregnancyWeeks = Math.floor(pregnancyDays / 7);
            const remainingDays = pregnancyDays % 7;
            
            let weekText = '';
            if (pregnancyDays < 0) {
                weekText = 'Invalid date: Last menstrual period date cannot be in the future.';
            } else if (pregnancyWeeks > 42) {
                weekText = 'It appears your due date has passed. Please consult with your healthcare provider.';
            } else {
                weekText = `You are currently ${pregnancyWeeks} weeks and ${remainingDays} days pregnant.`;
            }
            
            resultElement.innerHTML = `
                <div class="result-card">
                    <h3>Your Estimated Due Date</h3>
                    <p class="due-date">${formattedDate}</p>
                    <p class="pregnancy-week">${weekText}</p>
                    <div class="trimester-info">
                        <p>First Trimester: Weeks 1-12</p>
                        <p>Second Trimester: Weeks 13-26</p>
                        <p>Third Trimester: Weeks 27-40</p>
                    </div>
                </div>
            `;
        });
    }
    
    // BMI Calculator
    const bmiCalculator = document.getElementById('bmiCalculator');
    if (bmiCalculator) {
        bmiCalculator.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const weight = parseFloat(document.getElementById('weight').value);
            const height = parseFloat(document.getElementById('height').value) / 100; // Convert cm to m
            const resultElement = document.getElementById('bmiResult');
            
            if (isNaN(weight) || isNaN(height) || height <= 0 || weight <= 0) {
                resultElement.innerHTML = '<div class="error-message">Please enter valid values for weight and height</div>';
                return;
            }
            
            // Calculate BMI
            const bmi = weight / (height * height);
            const roundedBmi = Math.round(bmi * 10) / 10;
            
            // Determine BMI category
            let category, advice, categoryClass;
            
            if (bmi < 18.5) {
                category = 'Underweight';
                advice = 'Consider consulting with your healthcare provider about healthy weight gain during pregnancy.';
                categoryClass = 'underweight';
            } else if (bmi >= 18.5 && bmi < 25) {
                category = 'Normal weight';
                advice = 'Maintain a balanced diet and regular exercise as recommended by your healthcare provider.';
                categoryClass = 'normal';
            } else if (bmi >= 25 && bmi < 30) {
                category = 'Overweight';
                advice = 'Focus on nutrient-dense foods and appropriate exercise. Consult your healthcare provider for personalized advice.';
                categoryClass = 'overweight';
            } else {
                category = 'Obese';
                advice = 'Work with your healthcare provider to develop a safe pregnancy plan that includes nutrition and appropriate physical activity.';
                categoryClass = 'obese';
            }
            
            resultElement.innerHTML = `
                <div class="result-card">
                    <h3>Your BMI Result</h3>
                    <p class="bmi-value">${roundedBmi}</p>
                    <p class="bmi-category ${categoryClass}">${category}</p>
                    <div class="bmi-advice">
                        <p>${advice}</p>
                        <p class="disclaimer">Note: BMI is just one health indicator. Always consult with your healthcare provider for personalized advice during pregnancy.</p>
                    </div>
                </div>
            `;
        });
    }
    
    // Growth Tracker
    const growthForm = document.getElementById('growthTrackerForm');
    if (growthForm) {
        // Load saved data if available
        const loadSavedData = () => {
            const savedData = localStorage.getItem('growthTrackerData');
            if (savedData) {
                const data = JSON.parse(savedData);
                displayGrowthData(data);
            }
        };
        
        // Display growth data in table and chart
        const displayGrowthData = (data) => {
            const tableBody = document.getElementById('growthTableBody');
            const chartContainer = document.getElementById('growthChart');
            
            if (!tableBody || !chartContainer || !data || data.length === 0) return;
            
            // Clear existing data
            tableBody.innerHTML = '';
            
            // Sort data by date
            data.sort((a, b) => new Date(a.date) - new Date(b.date));
            
            // Populate table
            data.forEach(entry => {
                const row = document.createElement('tr');
                
                const dateCell = document.createElement('td');
                const date = new Date(entry.date);
                dateCell.textContent = date.toLocaleDateString('en-US');
                
                const ageCell = document.createElement('td');
                ageCell.textContent = entry.age;
                
                const weightCell = document.createElement('td');
                weightCell.textContent = `${entry.weight} ${entry.weightUnit}`;
                
                const heightCell = document.createElement('td');
                heightCell.textContent = `${entry.height} ${entry.heightUnit}`;
                
                const headCell = document.createElement('td');
                headCell.textContent = entry.headCircumference ? `${entry.headCircumference} cm` : '-';
                
                row.appendChild(dateCell);
                row.appendChild(ageCell);
                row.appendChild(weightCell);
                row.appendChild(heightCell);
                row.appendChild(headCell);
                
                tableBody.appendChild(row);
            });
            
            // Simple chart visualization (in a real app, you would use a charting library)
            chartContainer.innerHTML = '<p>Chart visualization would be displayed here using a library like Chart.js</p>';
        };
        
        // Add new growth data
        growthForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const date = document.getElementById('measurementDate').value;
            const age = document.getElementById('babyAge').value;
            const weight = document.getElementById('babyWeight').value;
            const weightUnit = document.getElementById('weightUnit').value;
            const height = document.getElementById('babyHeight').value;
            const heightUnit = document.getElementById('heightUnit').value;
            const headCircumference = document.getElementById('headCircumference').value;
            
            if (!date || !age || !weight || !height) {
                alert('Please fill in all required fields');
                return;
            }
            
            // Create new entry
            const newEntry = {
                date,
                age,
                weight,
                weightUnit,
                height,
                heightUnit,
                headCircumference
            };
            
            // Get existing data or initialize empty array
            let existingData = [];
            const savedData = localStorage.getItem('growthTrackerData');
            if (savedData) {
                existingData = JSON.parse(savedData);
            }
            
            // Add new entry
            existingData.push(newEntry);
            
            // Save to localStorage
            localStorage.setItem('growthTrackerData', JSON.stringify(existingData));
            
            // Update display
            displayGrowthData(existingData);
            
            // Reset form
            growthForm.reset();
            
            // Show success message
            alert('Growth data saved successfully!');
        });
        
        // Load data when page loads
        loadSavedData();
    }
    
    // Vaccination Schedule
    const vaccinationSchedule = document.getElementById('vaccinationSchedule');
    if (vaccinationSchedule) {
        const toggleVaccineDetails = (e) => {
            if (e.target.classList.contains('vaccine-toggle') || e.target.parentElement.classList.contains('vaccine-toggle')) {
                const button = e.target.classList.contains('vaccine-toggle') ? e.target : e.target.parentElement;
                const detailsContainer = button.nextElementSibling;
                
                // Toggle visibility
                if (detailsContainer.style.maxHeight) {
                    detailsContainer.style.maxHeight = null;
                    button.querySelector('i').className = 'fas fa-chevron-down';
                } else {
                    detailsContainer.style.maxHeight = detailsContainer.scrollHeight + 'px';
                    button.querySelector('i').className = 'fas fa-chevron-up';
                }
            }
        };
        
        // Add click event listener to the schedule container
        vaccinationSchedule.addEventListener('click', toggleVaccineDetails);
        
        // Set reminder functionality
        const reminderForms = document.querySelectorAll('.reminder-form');
        reminderForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const vaccineName = this.getAttribute('data-vaccine');
                const reminderDate = this.querySelector('input[type="date"]').value;
                
                if (!reminderDate) {
                    alert('Please select a date for the reminder');
                    return;
                }
                
                // In a real app, this would connect to a backend to set reminders
                // For now, we'll just store in localStorage
                let reminders = {};
                const savedReminders = localStorage.getItem('vaccineReminders');
                if (savedReminders) {
                    reminders = JSON.parse(savedReminders);
                }
                
                reminders[vaccineName] = reminderDate;
                localStorage.setItem('vaccineReminders', JSON.stringify(reminders));
                
                alert(`Reminder set for ${vaccineName} on ${new Date(reminderDate).toLocaleDateString()}`);
                
                // Update UI
                const reminderStatus = this.nextElementSibling;
                reminderStatus.textContent = `Reminder set for: ${new Date(reminderDate).toLocaleDateString()}`;
                reminderStatus.style.display = 'block';
            });
        });
        
        // Load existing reminders
        const loadReminders = () => {
            const savedReminders = localStorage.getItem('vaccineReminders');
            if (savedReminders) {
                const reminders = JSON.parse(savedReminders);
                
                for (const vaccine in reminders) {
                    const form = document.querySelector(`.reminder-form[data-vaccine="${vaccine}"]`);
                    if (form) {
                        const reminderStatus = form.nextElementSibling;
                        reminderStatus.textContent = `Reminder set for: ${new Date(reminders[vaccine]).toLocaleDateString()}`;
                        reminderStatus.style.display = 'block';
                    }
                }
            }
        };
        
        loadReminders();
    }
});
