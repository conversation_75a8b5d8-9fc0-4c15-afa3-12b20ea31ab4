const express = require('express');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');
const cookieParser = require('cookie-parser');
const connectDB = require('./config/db');

// Load env vars
dotenv.config();

// Connect to database
(async () => {
  const dbConnected = await connectDB();
  fs.appendFileSync('server-log.txt', `Database connection status: ${dbConnected ? 'Connected' : 'Failed'}\n`);

  if (!dbConnected) {
    fs.appendFileSync('server-log.txt', 'Warning: Server will run without database connection. Authentication features will not work.\n');
  }
})();

const app = express();
const PORT = process.env.PORT || 8080;

// Write to a file to confirm the server is starting
fs.writeFileSync('server-log.txt', `Server starting at ${new Date().toISOString()}\n`);

// Import routes
const pageRoutes = require('./routes/pages');
const apiRoutes = require('./routes/api');

// Import middleware
const { logger, errorHandler, notFoundHandler } = require('./middleware');

// Middleware
// Logger middleware
app.use(logger);

// Body parser
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Cookie parser
app.use(cookieParser());

// Set cache control headers to prevent caching issues
app.use((req, res, next) => {
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  next();
});

// Add a request logger to help debug routing issues
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - Request: ${req.method} ${req.url}`);
  next();
});

// Use API routes first
app.use('/api', apiRoutes);

// Serve static files from the 'public' directory with proper MIME types
// This should come after API routes but before page routes
app.use(express.static(path.join(__dirname, 'public'), {
  setHeaders: (res, path) => {
    // Set proper content type for HTML files
    if (path.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html');
    }
  }
}));

// Use page routes - these will handle clean URLs without .html extension
app.use('/', pageRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start the server
try {
  console.log('Starting server...');
  fs.appendFileSync('server-log.txt', 'Attempting to start server...\n');

  app.listen(PORT, () => {
    const message = `Server running on http://localhost:${PORT}`;
    console.log(message);
    console.log(`Visit http://localhost:${PORT} in your browser to view the application`);
    fs.appendFileSync('server-log.txt', message + '\n');
  });
} catch (error) {
  console.error('Error starting server:', error);
  fs.appendFileSync('server-log.txt', `Error starting server: ${error.message}\n`);
}
