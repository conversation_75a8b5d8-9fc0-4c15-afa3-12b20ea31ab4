/* Base Styles and Variables */
:root {
    /* Pastel Color Palette */
    --primary-color: #f8c6d1; /* Light Pink */
    --primary-dark: #e6a4b4;
    --secondary-color: #c5e3f6; /* Light Blue */
    --secondary-dark: #a3c9e9;
    --accent-color: #f9eac2; /* Light Cream */
    --accent-dark: #f0d89e;
    --text-color: #4a4a4a;
    --text-light: #6e6e6e;
    --white: #ffffff;
    --light-bg: #f9f9f9;
    --border-color: #e0e0e0;
    --success-color: #a8d5ba; /* Soft Green */
    --warning-color: #f9d5a7; /* Soft Orange */
    --error-color: #f8c6c6; /* Soft Red */

    /* Typography */
    --heading-font: 'Montserrat', sans-serif;
    --body-font: 'Open Sans', sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--body-font);
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--white);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--primary-dark);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-color);
}

img {
    max-width: 100%;
    height: auto;
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    position: relative;
    padding-bottom: var(--spacing-md);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-family: var(--heading-font);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    color: var(--white);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    color: var(--white);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Header and Navigation */
header {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    padding: var(--spacing-md) 0;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--primary-dark);
    font-weight: 700;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-menu li {
    margin-left: var(--spacing-lg);
}

.nav-menu a {
    color: var(--text-color);
    font-weight: 500;
    position: relative;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
    width: 100%;
}

.btn-login {
    background-color: var(--accent-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
}

.btn-login:hover {
    background-color: var(--accent-dark);
}

.btn-register {
    background-color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
    margin-left: var(--spacing-sm);
}

.btn-register:hover {
    background-color: var(--primary-dark);
    color: var(--white);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
}

.bar {
    display: block;
    width: 25px;
    height: 3px;
    margin: 5px auto;
    background-color: var(--text-color);
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: var(--spacing-xxl) 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xl);
    color: var(--text-color);
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

/* Features Section */
.features {
    padding: var(--spacing-xxl) 0;
    background-color: var(--white);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    background-color: var(--light-bg);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--primary-dark);
    margin-bottom: var(--spacing-md);
}

.feature-card h3 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
}

.feature-link {
    display: inline-block;
    margin-top: var(--spacing-sm);
    font-weight: 600;
    color: var(--primary-dark);
}

.feature-link:hover {
    color: var(--primary-color);
}

/* Tools Preview Section */
.tools-preview {
    padding: var(--spacing-xxl) 0;
    background-color: var(--light-bg);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.tool-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    text-align: center;
}

.tool-card h3 {
    color: var(--primary-dark);
    font-size: 1.25rem;
    margin-bottom: var(--spacing-md);
}

.tool-card p {
    margin-bottom: var(--spacing-lg);
}

/* Community Preview Section */
.community-preview {
    padding: var(--spacing-xxl) 0;
    background-color: var(--white);
}

.community-preview .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.community-content h2 {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
}

.community-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

/* Articles Preview Section */
.articles-preview {
    padding: var(--spacing-xxl) 0;
    background-color: var(--light-bg);
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.article-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.article-image {
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.article-content {
    padding: var(--spacing-lg);
}

.article-category {
    display: inline-block;
    background-color: var(--accent-color);
    color: var(--text-color);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
}

.article-content h3 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
}

.article-link {
    display: inline-block;
    margin-top: var(--spacing-sm);
    font-weight: 600;
    color: var(--primary-dark);
}

.articles-cta {
    text-align: center;
}

/* Footer */
footer {
    background-color: var(--text-color);
    color: var(--white);
    padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-about h3 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--white);
    transition: background-color 0.3s ease;
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: var(--text-color);
}

.footer-links h4,
.footer-tools h4,
.footer-newsletter h4 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.footer-links ul li,
.footer-tools ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a,
.footer-tools a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s ease;
}

.footer-links a:hover,
.footer-tools a:hover {
    color: var(--primary-color);
}

.newsletter-form {
    display: flex;
    margin-top: var(--spacing-md);
}

.newsletter-form input {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    font-family: var(--body-font);
}

.newsletter-form button {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    background-color: var(--primary-color);
    color: var(--text-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-bottom-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.footer-bottom-links a:hover {
    color: var(--primary-color);
}

/* Form Styles */
.form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-family: var(--body-font);
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group .checkbox-group {
    display: flex;
    align-items: center;
}

.form-group .checkbox-group input {
    width: auto;
    margin-right: var(--spacing-sm);
}

.form-group .error-message {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
}

/* Auth Pages */
.auth-container {
    max-width: 450px;
    margin: 0 auto;
    padding: var(--spacing-xl);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-header h1 {
    color: var(--primary-dark);
    margin-bottom: var(--spacing-sm);
}

.auth-header p {
    color: var(--text-light);
}

.auth-form .form-group {
    margin-bottom: var(--spacing-lg);
}

.auth-form label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.auth-form input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-family: var(--body-font);
    transition: border-color 0.3s ease;
}

.auth-form input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.auth-form .password-field {
    position: relative;
}

.auth-form .toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
}

.auth-form .form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
}

.auth-form .btn-submit {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--primary-color);
    color: var(--text-color);
    border: none;
    border-radius: var(--border-radius-md);
    font-family: var(--heading-font);
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: var(--spacing-lg);
}

.auth-form .btn-submit:hover {
    background-color: var(--primary-dark);
    color: var(--white);
}

.auth-divider {
    display: flex;
    align-items: center;
    margin: var(--spacing-lg) 0;
    color: var(--text-light);
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background-color: var(--border-color);
}

.auth-divider span {
    padding: 0 var(--spacing-md);
}

.social-auth {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

.social-auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background-color: var(--white);
    color: var(--text-color);
    font-family: var(--body-font);
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    flex: 1;
}

.social-auth-btn i {
    margin-right: var(--spacing-sm);
}

.social-auth-btn.google:hover {
    background-color: #f2f2f2;
}

.social-auth-btn.facebook {
    background-color: #4267B2;
    color: white;
    border-color: #4267B2;
}

.social-auth-btn.facebook:hover {
    background-color: #365899;
}

.auth-switch {
    text-align: center;
    margin-top: var(--spacing-lg);
    color: var(--text-light);
}

.auth-switch a {
    color: var(--primary-dark);
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: var(--spacing-xl) 0;
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.page-header p {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
}

/* Placeholder Images */
.img-placeholder {
    background-color: var(--secondary-color);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    font-weight: 600;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .community-preview .container {
        grid-template-columns: 1fr;
    }

    .community-image {
        order: -1;
    }

    .community-content {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-md);
        padding: var(--spacing-lg) 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: var(--spacing-md) 0;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .feature-grid,
    .tools-grid,
    .articles-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 1.5rem;
    }
}
