<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password | MATERNIFY</title>
    <meta name="description" content="Reset your MATERNIFY account password to regain access to your personalized pregnancy and infant care tools.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                    <li><a href="/infant-care">Infant Care</a></li>
                    <li><a href="/tools">Tools</a></li>
                    <li><a href="/community">Community</a></li>
                    <li><a href="/blog">Articles</a></li>
                    <li><a href="/login" class="btn-login">Login</a></li>
                    <li><a href="/register" class="btn-register">Register</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-section" style="padding: var(--spacing-xxl) 0; background-color: var(--light-bg);">
        <div class="container">
            <div class="auth-container">
                <div class="auth-header">
                    <h1>Forgot Password</h1>
                    <p>Enter your email address to receive a password reset link</p>
                </div>

                <form class="auth-form" id="forgotPasswordForm">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" name="email" required>
                        <div class="error-message" id="emailError" style="display: none;">Please enter a valid email address</div>
                    </div>

                    <button type="submit" class="btn-submit">Send Reset Link</button>
                </form>

                <div id="resetSuccess" style="display: none; margin-top: 20px; text-align: center; padding: 15px; background-color: #d4edda; color: #155724; border-radius: 5px;">
                    <p>Password reset link has been sent to your email address. Please check your inbox and follow the instructions.</p>
                </div>

                <div class="auth-switch" style="margin-top: 20px;">
                    <a href="/login">Back to Login</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                        <li><a href="/infant-care">Infant Care</a></li>
                        <li><a href="/tools">Tools</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/blog">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/tools#due-date">Due Date Calculator</a></li>
                        <li><a href="/tools#bmi">BMI Tracker</a></li>
                        <li><a href="/tools#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/tools#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const forgotPasswordForm = document.getElementById('forgotPasswordForm');
            const resetSuccess = document.getElementById('resetSuccess');
            
            if (forgotPasswordForm) {
                forgotPasswordForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    
                    const email = document.getElementById('email').value;
                    const emailError = document.getElementById('emailError');
                    
                    // Validate email
                    if (!email || !isValidEmail(email)) {
                        emailError.style.display = 'block';
                        return;
                    } else {
                        emailError.style.display = 'none';
                    }
                    
                    // Show loading state
                    const submitButton = forgotPasswordForm.querySelector('.btn-submit');
                    const originalText = submitButton.textContent;
                    submitButton.textContent = 'Sending...';
                    submitButton.disabled = true;
                    
                    try {
                        // Check if API service is available
                        if (!window.apiService) {
                            console.error('API service not found. Make sure api-service.js is loaded before this script');
                            alert('An error occurred. Please try again later.');
                            
                            // Reset button
                            submitButton.textContent = originalText;
                            submitButton.disabled = false;
                            return;
                        }
                        
                        // Call the forgot password API
                        const result = await apiService.forgotPassword(email);
                        
                        if (result.success) {
                            // Show success message
                            forgotPasswordForm.style.display = 'none';
                            resetSuccess.style.display = 'block';
                        } else {
                            // Show error message
                            alert(result.message || 'An error occurred. Please try again.');
                            
                            // Reset button
                            submitButton.textContent = originalText;
                            submitButton.disabled = false;
                        }
                    } catch (error) {
                        console.error('Forgot password error:', error);
                        alert('An error occurred. Please try again.');
                        
                        // Reset button
                        submitButton.textContent = originalText;
                        submitButton.disabled = false;
                    }
                });
            }
            
            // Helper function to validate email
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
        });
    </script>
</body>
</html>
