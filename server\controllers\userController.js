const User = require('../models/User');
const Activity = require('../models/Activity');

// Get all users (admin only)
exports.getAllUsers = async (req, res, next) => {
  try {
    // Implement pagination, filtering, and sorting
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;

    // Build query
    let query = User.find();

    // Filter by role
    if (req.query.role) {
      query = query.find({ role: req.query.role });
    }

    // Filter by active status
    if (req.query.active) {
      query = query.find({ active: req.query.active === 'true' });
    }

    // Search by name or email
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      query = query.find({
        $or: [
          { firstName: searchRegex },
          { lastName: searchRegex },
          { email: searchRegex },
        ],
      });
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // Execute query with pagination
    const users = await query.skip(skip).limit(limit);

    // Get total count for pagination
    const total = await User.countDocuments(query.getFilter());

    res.status(200).json({
      success: true,
      count: users.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
      data: users,
    });
  } catch (error) {
    next(error);
  }
};

// Get user by ID (admin only)
exports.getUserById = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    next(error);
  }
};

// Create new user (admin only)
exports.createUser = async (req, res, next) => {
  try {
    const { firstName, lastName, email, password, role, phone } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Email already in use',
      });
    }

    // Create new user
    const newUser = await User.create({
      firstName,
      lastName,
      email,
      password,
      role,
      phone,
    });

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'user_create',
      details: `Admin created user: ${email}`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(201).json({
      success: true,
      data: newUser,
    });
  } catch (error) {
    next(error);
  }
};

// Update user (admin or own account)
exports.updateUser = async (req, res, next) => {
  try {
    // Check if user is updating their own account or is admin
    const isAdmin = req.user.role === 'admin';
    const isOwnAccount = req.user.id === req.params.id;

    if (!isAdmin && !isOwnAccount) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to update this user',
      });
    }

    // Filter allowed fields
    const filteredBody = {};
    const allowedFields = ['firstName', 'lastName', 'phone', 'pregnancyDueDate', 'babyBirthdate', 'preferences'];
    
    // Admin can update more fields
    if (isAdmin) {
      allowedFields.push('role', 'active');
    }

    Object.keys(req.body).forEach(field => {
      if (allowedFields.includes(field)) {
        filteredBody[field] = req.body[field];
      }
    });

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      filteredBody,
      {
        new: true,
        runValidators: true,
      }
    );

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'user_update',
      details: `User updated: ${updatedUser.email}`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(200).json({
      success: true,
      data: updatedUser,
    });
  } catch (error) {
    next(error);
  }
};

// Delete user (admin only)
exports.deleteUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Don't allow deleting the last admin
    if (user.role === 'admin') {
      const adminCount = await User.countDocuments({ role: 'admin' });
      if (adminCount <= 1) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete the last admin user',
        });
      }
    }

    await User.findByIdAndDelete(req.params.id);

    // Log activity
    await Activity.create({
      user: req.user._id,
      action: 'user_delete',
      details: `User deleted: ${user.email}`,
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });

    res.status(200).json({
      success: true,
      message: 'User deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};
