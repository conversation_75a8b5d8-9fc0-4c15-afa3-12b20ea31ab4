const express = require('express');
const router = express.Router();

// Import controllers
const authController = require('../controllers/authController');
const toolsController = require('../controllers/toolsController');
const userController = require('../controllers/userController');

// Import middleware
const { protect } = require('../middleware/auth');

// Authentication routes
router.post('/login', authController.login);
router.post('/register', authController.register);
router.get('/logout', authController.logout);  // Keep GET for backward compatibility
router.post('/logout', authController.logout); // Add POST method for logout
router.get('/me', protect, authController.getMe);
router.post('/refresh-token', authController.refreshToken);

// Password reset routes
router.post('/forgot-password', authController.forgotPassword);
router.post('/reset-password/:token', authController.resetPassword);

// Email verification route
router.get('/verify-email/:token', authController.verifyEmail);

// Newsletter routes
router.post('/subscribe', authController.subscribe);
router.get('/unsubscribe/:token', authController.unsubscribe);

// User profile routes
router.put('/profile', protect, userController.updateProfile);
router.put('/password', protect, userController.updatePassword);

// Tools routes
router.post('/calculate-due-date', toolsController.calculateDueDate);
router.post('/calculate-bmi', toolsController.calculateBMI);

module.exports = router;
