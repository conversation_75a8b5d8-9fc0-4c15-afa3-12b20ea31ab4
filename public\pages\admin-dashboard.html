<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | MATERNIFY</title>
    <meta name="description" content="Admin dashboard for managing MATERNIFY platform.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Custom styles for header to match footer -->
    <style>
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        .user-menu {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        .user-greeting {
            color: var(--white) !important;
        }
        .logout-btn {
            color: var(--white) !important;
        }
        .logout-btn:hover {
            color: var(--primary-color) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }
    </style>
    <style>
        /* Dashboard Layout */
        .dashboard-layout {
            display: flex;
            min-height: calc(100vh - 70px - 300px); /* Adjust based on header and footer height */
        }

        /* Sidebar Styles */
        .dashboard-sidebar {
            width: 250px;
            background-color: var(--text-color); /* Match header color */
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: var(--spacing-md);
            position: sticky;
            top: 70px; /* Adjust based on header height */
            height: calc(100vh - 70px); /* Adjust based on header height */
            overflow-y: auto;
            transition: transform 0.3s ease;
            color: var(--white);
        }

        .sidebar-header {
            padding-bottom: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--white);
        }

        .sidebar-user {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: var(--white);
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: var(--white);
        }

        .user-role {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .sidebar-nav {
            margin-bottom: var(--spacing-lg);
        }

        .sidebar-section {
            margin-bottom: var(--spacing-md);
        }

        .sidebar-section-title {
            font-size: 0.9rem;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.5);
            margin-bottom: var(--spacing-sm);
            padding-left: var(--spacing-sm);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu-item {
            margin-bottom: 2px;
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color);
        }

        .sidebar-menu-link.active {
            background-color: rgba(255, 255, 255, 0.15);
            color: var(--primary-color);
            font-weight: 500;
        }

        .sidebar-menu-icon {
            margin-right: var(--spacing-sm);
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            padding-top: var(--spacing-md);
            margin-top: auto;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-footer .logout-btn {
            display: flex;
            align-items: center;
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .sidebar-footer .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-color);
        }

        .logout-icon {
            margin-right: var(--spacing-sm);
        }

        /* Main Content Styles */
        .dashboard-main {
            flex: 1;
            padding: var(--spacing-xl);
            background-color: var(--white);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
        }

        .dashboard-title {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .dashboard-welcome {
            font-size: 1rem;
            color: var(--text-light);
        }

        .dashboard-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* Admin-specific styles */
        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background-color: var(--white);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-md);
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-light);
        }

        .admin-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
            overflow-x: auto;
        }

        .admin-tab {
            padding: var(--spacing-sm) var(--spacing-lg);
            cursor: pointer;
            border-bottom: 3px solid transparent;
            font-weight: 500;
            color: var(--text-light);
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .admin-tab:hover {
            color: var(--primary-color);
        }

        .admin-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .admin-tab-content {
            display: none;
        }

        .admin-tab-content.active {
            display: block;
        }

        .admin-card {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            border: 1px solid var(--border-color);
        }

        .admin-card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
        }

        .admin-card-title i {
            margin-right: var(--spacing-sm);
            color: var(--primary-color);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-sm);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background-color: var(--light-bg);
            font-weight: 600;
            color: var(--text-color);
        }

        .data-table tr:hover {
            background-color: rgba(var(--primary-rgb), 0.05);
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-icon {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background-color: var(--accent-color);
            color: var(--white);
        }

        .btn-delete {
            background-color: #f44336;
            color: var(--white);
        }

        .btn-view {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-icon:hover {
            opacity: 0.8;
        }

        .search-filter {
            display: flex;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 200px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: 0.9rem;
        }

        .search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }

        .filter-dropdown {
            min-width: 150px;
        }

        .filter-dropdown select {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-size: 0.9rem;
            background-color: var(--white);
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: var(--spacing-lg);
            gap: 5px;
        }

        .pagination-btn {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            background-color: var(--white);
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination-btn.active {
            background-color: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }

        .pagination-btn:hover:not(.active) {
            background-color: var(--light-bg);
        }

        /* Mobile Toggle Button */
        .sidebar-toggle {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            box-shadow: var(--shadow-md);
            z-index: 100;
            cursor: pointer;
            align-items: center;
            justify-content: center;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .dashboard-sidebar {
                position: fixed;
                left: 0;
                z-index: 1000;
                transform: translateX(-100%);
                box-shadow: var(--shadow-md);
            }

            .dashboard-sidebar.active {
                transform: translateX(0);
            }

            .sidebar-toggle {
                display: flex;
            }

            .dashboard-main {
                width: 100%;
            }

            .admin-stats {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .admin-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .dashboard-actions {
                width: 100%;
            }

            .dashboard-actions .btn {
                flex: 1;
                text-align: center;
            }

            .search-filter {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li>
                        <div class="user-menu">
                            <span class="user-greeting">Hello, <span id="nav-user-name">Admin</span>!</span>
                            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Mobile Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="dashboardSidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">ADMIN PANEL</h3>
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <span id="user-initial">A</span>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="sidebar-user-name">Admin</div>
                        <div class="user-role">Administrator</div>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <!-- Admin Navigation -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">Management</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="/pages/admin-dashboard.html" class="sidebar-menu-link active">
                                <span class="sidebar-menu-icon"><i class="fas fa-tachometer-alt"></i></span>
                                Dashboard
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#users" class="sidebar-menu-link" data-tab="users">
                                <span class="sidebar-menu-icon"><i class="fas fa-users"></i></span>
                                User Management
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#content" class="sidebar-menu-link" data-tab="content">
                                <span class="sidebar-menu-icon"><i class="fas fa-file-alt"></i></span>
                                Content Management
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#analytics" class="sidebar-menu-link" data-tab="analytics">
                                <span class="sidebar-menu-icon"><i class="fas fa-chart-bar"></i></span>
                                Analytics
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- System Navigation -->
                <div class="sidebar-section">
                    <h4 class="sidebar-section-title">System</h4>
                    <ul class="sidebar-menu">
                        <li class="sidebar-menu-item">
                            <a href="#settings" class="sidebar-menu-link" data-tab="settings">
                                <span class="sidebar-menu-icon"><i class="fas fa-cog"></i></span>
                                Settings
                            </a>
                        </li>
                        <li class="sidebar-menu-item">
                            <a href="#logs" class="sidebar-menu-link" data-tab="logs">
                                <span class="sidebar-menu-icon"><i class="fas fa-list"></i></span>
                                System Logs
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="sidebar-footer">
                <a href="#" class="logout-btn" id="sidebarLogoutBtn">
                    <span class="logout-icon"><i class="fas fa-sign-out-alt"></i></span>
                    Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <div class="dashboard-header">
                <div>
                    <h1 class="dashboard-title">Admin Dashboard</h1>
                    <p class="dashboard-welcome">Welcome back, <span id="user-name">Admin</span>!</p>
                </div>
                <div class="dashboard-actions">
                    <a href="#" class="btn btn-primary" id="refreshDataBtn"><i class="fas fa-sync-alt"></i> Refresh Data</a>
                    <a href="#settings" class="btn btn-secondary" data-tab="settings"><i class="fas fa-cog"></i> Settings</a>
                </div>
            </div>

            <!-- Statistics Overview -->
            <div class="admin-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value" id="totalUsers">0</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stat-value" id="newUsers">0</div>
                    <div class="stat-label">New Users (30d)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-value" id="totalContent">0</div>
                    <div class="stat-label">Content Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="stat-value" id="totalViews">0</div>
                    <div class="stat-label">Page Views</div>
                </div>
            </div>

            <!-- Admin Tabs -->
            <div class="admin-tabs">
                <div class="admin-tab active" data-tab="dashboard">Dashboard</div>
                <div class="admin-tab" data-tab="users">User Management</div>
                <div class="admin-tab" data-tab="content">Content Management</div>
                <div class="admin-tab" data-tab="analytics">Analytics</div>
                <div class="admin-tab" data-tab="settings">Settings</div>
                <div class="admin-tab" data-tab="logs">System Logs</div>
            </div>

            <!-- Tab Content -->
            <div id="dashboard-tab" class="admin-tab-content active">
                <div class="admin-card">
                    <h3 class="admin-card-title"><i class="fas fa-chart-line"></i> Recent Activity</h3>
                    <div class="search-filter">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="Search activities...">
                        </div>
                        <div class="filter-dropdown">
                            <select>
                                <option value="all">All Activities</option>
                                <option value="login">Logins</option>
                                <option value="registration">Registrations</option>
                                <option value="content">Content Updates</option>
                            </select>
                        </div>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date & Time</th>
                                <th>User</th>
                                <th>Activity</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody id="activity-table-body">
                            <!-- Activity data will be loaded here -->
                        </tbody>
                    </table>
                    <div class="pagination" id="activity-pagination">
                        <!-- Pagination will be generated here -->
                    </div>
                </div>

                <div class="admin-card">
                    <h3 class="admin-card-title"><i class="fas fa-exclamation-triangle"></i> System Alerts</h3>
                    <div id="alerts-container">
                        <!-- Alerts will be loaded here -->
                    </div>
                </div>
            </div>

            <div id="users-tab" class="admin-tab-content">
                <!-- User management content will be loaded here -->
            </div>

            <div id="content-tab" class="admin-tab-content">
                <!-- Content management will be loaded here -->
            </div>

            <div id="analytics-tab" class="admin-tab-content">
                <!-- Analytics content will be loaded here -->
            </div>

            <div id="settings-tab" class="admin-tab-content">
                <!-- Settings content will be loaded here -->
            </div>

            <div id="logs-tab" class="admin-tab-content">
                <!-- System logs will be loaded here -->
            </div>
        </main>
    </div>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pregnancy-care">Pregnancy Care</a></li>
                        <li><a href="/infant-care">Infant Care</a></li>
                        <li><a href="/tools">Tools</a></li>
                        <li><a href="/community">Community</a></li>
                        <li><a href="/blog">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Admin Tools</h4>
                    <ul>
                        <li><a href="#users" data-tab="users">User Management</a></li>
                        <li><a href="#content" data-tab="content">Content Management</a></li>
                        <li><a href="#analytics" data-tab="analytics">Analytics</a></li>
                        <li><a href="#settings" data-tab="settings">Settings</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Admin Support</h4>
                    <p>Need help with the admin panel? Contact our support team.</p>
                    <a href="mailto:<EMAIL>" class="btn btn-sm">Contact Support</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/logout.js"></script>
    <script src="/js/admin-api.js"></script>
    <script src="/js/admin-dashboard.js"></script>
</body>
</html>
