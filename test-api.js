const fs = require('fs');
const http = require('http');

// Function to make a POST request
function postRequest(path, data) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8080,
      path,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(JSON.stringify(data))
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(JSON.stringify(data));
    req.end();
  });
}

// Test registration
async function testRegistration() {
  try {
    console.log('Testing registration API...');
    fs.appendFileSync('api-test-log.txt', 'Testing registration API...\n');

    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'password123'
    };

    const response = await postRequest('/api/register', userData);
    
    console.log('Registration response:', response);
    fs.appendFileSync('api-test-log.txt', `Registration response: ${JSON.stringify(response, null, 2)}\n`);
    
    return response;
  } catch (error) {
    console.error('Registration test error:', error);
    fs.appendFileSync('api-test-log.txt', `Registration test error: ${error.message}\n`);
    return { error: error.message };
  }
}

// Test login
async function testLogin() {
  try {
    console.log('Testing login API...');
    fs.appendFileSync('api-test-log.txt', 'Testing login API...\n');

    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const response = await postRequest('/api/login', loginData);
    
    console.log('Login response:', response);
    fs.appendFileSync('api-test-log.txt', `Login response: ${JSON.stringify(response, null, 2)}\n`);
    
    return response;
  } catch (error) {
    console.error('Login test error:', error);
    fs.appendFileSync('api-test-log.txt', `Login test error: ${error.message}\n`);
    return { error: error.message };
  }
}

// Run tests
async function runTests() {
  fs.writeFileSync('api-test-log.txt', `API Tests started at ${new Date().toISOString()}\n`);
  
  // Test registration
  const registrationResponse = await testRegistration();
  
  // Test login
  const loginResponse = await testLogin();
  
  fs.appendFileSync('api-test-log.txt', `API Tests completed at ${new Date().toISOString()}\n`);
  
  console.log('Tests completed. Check api-test-log.txt for details.');
}

// Run the tests
runTests();
