<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parenting Tools | MATERNIFY</title>
    <meta name="description" content="Interactive tools for pregnancy and infant care including due date calculator, BMI tracker, growth tracking, and vaccination schedule.">
    <link rel="stylesheet" href="../css/styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Chart.js for Growth Tracker -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../js/logout.js"></script>
    <style>
        /* Custom styles for header to match footer */
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        .user-menu {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        .user-greeting {
            color: var(--white) !important;
        }
        .logout-btn {
            color: var(--white) !important;
        }
        .logout-btn:hover {
            color: var(--primary-color) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }

        /* Page-specific styles */
        .tools-nav {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .tool-nav-item {
            padding: var(--spacing-md) var(--spacing-lg);
            background-color: var(--light-bg);
            border-radius: var(--border-radius-md);
            font-family: var(--heading-font);
            font-weight: 500;
            color: var(--text-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .tool-nav-item i {
            margin-right: var(--spacing-sm);
            font-size: 1.2rem;
        }

        .tool-nav-item:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .tool-section {
            padding: var(--spacing-xl) 0;
            border-top: 1px solid var(--border-color);
            scroll-margin-top: 100px;
        }

        .tool-section:first-of-type {
            border-top: none;
        }

        .tool-header {
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
        }

        .tool-header i {
            font-size: 2rem;
            color: var(--primary-dark);
            margin-right: var(--spacing-md);
        }

        .tool-header h2 {
            margin-bottom: 0;
        }

        .tool-description {
            margin-bottom: var(--spacing-lg);
            max-width: 800px;
        }

        .tool-container {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-actions {
            margin-top: var(--spacing-lg);
            display: flex;
            justify-content: flex-end;
        }

        .result-card {
            background-color: var(--light-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .result-card h3 {
            color: var(--primary-dark);
            margin-bottom: var(--spacing-md);
            text-align: center;
        }

        .due-date, .bmi-value {
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: var(--spacing-md);
            color: var(--primary-dark);
        }

        .pregnancy-week, .bmi-category {
            text-align: center;
            margin-bottom: var(--spacing-md);
            font-weight: 500;
        }

        .trimester-info, .bmi-advice {
            background-color: var(--white);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
        }

        .bmi-category.underweight { color: #3498db; }
        .bmi-category.normal { color: #2ecc71; }
        .bmi-category.overweight { color: #f39c12; }
        .bmi-category.obese { color: #e74c3c; }

        .disclaimer {
            font-size: 0.875rem;
            font-style: italic;
            margin-top: var(--spacing-md);
            color: var(--text-light);
        }

        .growth-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: var(--spacing-lg);
        }

        .growth-table th, .growth-table td {
            padding: var(--spacing-sm);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .growth-table th {
            background-color: var(--light-bg);
            font-weight: 600;
        }

        .growth-chart {
            background-color: var(--light-bg);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-md);
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .vaccine-age-group {
            margin-bottom: var(--spacing-xl);
        }

        .vaccine-age-header {
            background-color: var(--secondary-color);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
            font-weight: 600;
        }

        .vaccine-list {
            background-color: var(--white);
            border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
            box-shadow: var(--shadow-sm);
        }

        .vaccine-item {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }

        .vaccine-item:last-child {
            border-bottom: none;
        }

        .vaccine-name {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .vaccine-toggle {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: var(--spacing-xs);
        }

        .vaccine-details {
            padding-top: var(--spacing-sm);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .vaccine-description {
            margin-bottom: var(--spacing-sm);
            font-size: 0.95rem;
        }

        .reminder-form {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .reminder-status {
            font-size: 0.875rem;
            color: var(--primary-dark);
            margin-top: var(--spacing-xs);
            display: none;
        }

        /* Growth Tracker Action Buttons */
        .actions-cell {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .edit-btn {
            color: var(--primary-color);
        }

        .delete-btn {
            color: var(--error-color);
        }

        .action-btn:hover {
            background-color: rgba(0, 0, 0, 0.05);
            transform: scale(1.1);
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        /* Fix for no data message */
        #growthTableBody tr td[colspan="6"] {
            text-align: center;
            padding: 20px;
        }

        /* Make the chart container taller for better visualization */
        .growth-chart {
            height: 450px;
            position: relative;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        /* Chart controls styling */
        .chart-controls {
            background-color: #fff;
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
        }

        .chart-controls select {
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: #fff;
            font-size: 14px;
        }

        .chart-controls label {
            font-weight: 500;
            margin-right: 8px;
        }

        /* Sample data button styling */
        #growthTrackerForm .form-actions .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        #growthTrackerForm .form-actions .btn-secondary:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li>
                        <div class="user-menu">
                            <span class="user-greeting">Hello, <span id="nav-user-name">User</span>!</span>
                            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Parenting Tools</h1>
            <p>Interactive tools to support you through pregnancy and infant care.</p>
        </div>
    </section>

    <section class="tools-overview">
        <div class="container">
            <div class="tools-nav">
                <a href="#due-date" class="tool-nav-item">
                    <i class="far fa-calendar-alt"></i> Due Date Calculator
                </a>
                <a href="#bmi" class="tool-nav-item">
                    <i class="fas fa-weight"></i> BMI Tracker
                </a>
                <a href="#growth-tracker" class="tool-nav-item">
                    <i class="fas fa-chart-line"></i> Growth Tracker
                </a>
                <a href="#vaccination" class="tool-nav-item">
                    <i class="fas fa-syringe"></i> Vaccination Schedule
                </a>
            </div>

            <!-- Due Date Calculator -->
            <div id="due-date" class="tool-section">
                <div class="tool-header">
                    <i class="far fa-calendar-alt"></i>
                    <h2>Due Date Calculator</h2>
                </div>
                <p class="tool-description">
                    Estimate your baby's due date based on your last menstrual period (LMP). This calculator uses the standard method of adding 280 days (40 weeks) to the first day of your last period.
                </p>
                <div class="tool-container">
                    <form id="dueDateCalculator">
                        <div class="form-group">
                            <label for="lmpDate">First day of your last menstrual period</label>
                            <input type="date" id="lmpDate" name="lmpDate" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Calculate Due Date</button>
                        </div>
                    </form>
                    <div id="dueDateResult"></div>
                </div>
                <p class="disclaimer">
                    Note: This calculator provides an estimate based on a standard 28-day cycle. For a more accurate due date, consult with your healthcare provider, especially if you have irregular cycles.
                </p>
            </div>

            <!-- BMI Tracker -->
            <div id="bmi" class="tool-section">
                <div class="tool-header">
                    <i class="fas fa-weight"></i>
                    <h2>BMI Tracker</h2>
                </div>
                <p class="tool-description">
                    Calculate your Body Mass Index (BMI) to monitor your weight during pregnancy. BMI is a measure of body fat based on height and weight that applies to adult women and men.
                </p>
                <div class="tool-container">
                    <form id="bmiCalculator">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="weight">Weight (kg)</label>
                                <input type="number" id="weight" name="weight" min="30" max="300" step="0.1" required>
                            </div>
                            <div class="form-group">
                                <label for="height">Height (cm)</label>
                                <input type="number" id="height" name="height" min="100" max="250" step="0.1" required>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Calculate BMI</button>
                        </div>
                    </form>
                    <div id="bmiResult"></div>
                </div>
                <p class="disclaimer">
                    Note: BMI is just one measure of health and may not be accurate for all body types. During pregnancy, weight gain recommendations vary based on pre-pregnancy BMI. Always consult with your healthcare provider for personalized advice.
                </p>
            </div>

            <!-- Growth Tracker -->
            <div id="growth-tracker" class="tool-section">
                <div class="tool-header">
                    <i class="fas fa-chart-line"></i>
                    <h2>Growth Tracker</h2>
                </div>
                <p class="tool-description">
                    Monitor your baby's growth by recording weight, height, and head circumference measurements over time. This tool helps you visualize your baby's development and share the information with your pediatrician.
                </p>
                <div class="tool-container">
                    <form id="growthTrackerForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="measurementDate">Date of Measurement</label>
                                <input type="date" id="measurementDate" name="measurementDate" required>
                            </div>
                            <div class="form-group">
                                <label for="babyAge">Baby's Age</label>
                                <input type="text" id="babyAge" name="babyAge" placeholder="e.g., 2 months" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="babyWeight">Weight</label>
                                <input type="number" id="babyWeight" name="babyWeight" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="weightUnit">Unit</label>
                                <select id="weightUnit" name="weightUnit">
                                    <option value="kg">Kilograms (kg)</option>
                                    <option value="lb">Pounds (lb)</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="babyHeight">Length/Height</label>
                                <input type="number" id="babyHeight" name="babyHeight" step="0.1" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="heightUnit">Unit</label>
                                <select id="heightUnit" name="heightUnit">
                                    <option value="cm">Centimeters (cm)</option>
                                    <option value="in">Inches (in)</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="headCircumference">Head Circumference (cm)</label>
                                <input type="number" id="headCircumference" name="headCircumference" step="0.1" min="0">
                            </div>
                        </div>
                        <div class="form-actions" style="display: flex; justify-content: space-between;">
                            <button type="button" id="clearGrowthData" class="btn btn-secondary">Clear All Data</button>
                            <button type="submit" class="btn btn-primary">Save Measurement</button>
                        </div>
                    </form>

                    <div class="growth-data" style="margin-top: var(--spacing-xl);">
                        <h3>Growth History</h3>
                        <div class="chart-controls" style="display: flex; justify-content: center; margin-bottom: var(--spacing-md);">
                            <div style="margin-right: var(--spacing-md);">
                                <label for="chartType">Chart Type:</label>
                                <select id="chartType">
                                    <option value="all">All Measurements</option>
                                    <option value="weight">Weight</option>
                                    <option value="height">Height</option>
                                    <option value="head">Head Circumference</option>
                                </select>
                            </div>
                            <div>
                                <label for="chartPeriod">Time Period:</label>
                                <select id="chartPeriod">
                                    <option value="all">All Time</option>
                                    <option value="6months">Last 6 Months</option>
                                    <option value="3months">Last 3 Months</option>
                                </select>
                            </div>
                        </div>
                        <div class="growth-chart">
                            <canvas id="growthChart" width="400" height="200"></canvas>
                        </div>
                        <div class="growth-table-container">
                            <table class="growth-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Age</th>
                                        <th>Weight</th>
                                        <th>Height</th>
                                        <th>Head Circ.</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="growthTableBody">
                                    <tr>
                                        <td colspan="6" style="text-align: center;">No data available yet</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <p class="disclaimer">
                    Note: This tool is for personal tracking only and does not replace professional medical advice. Always consult with your pediatrician about your baby's growth and development.
                </p>
            </div>

            <!-- Vaccination Schedule -->
            <div id="vaccination" class="tool-section">
                <div class="tool-header">
                    <i class="fas fa-syringe"></i>
                    <h2>Vaccination Schedule</h2>
                </div>
                <p class="tool-description">
                    Keep track of recommended vaccines for your child based on age. This schedule follows standard guidelines, but your healthcare provider may recommend a different schedule based on your child's specific needs.
                </p>
                <div class="tool-container">
                    <div id="vaccinationSchedule">
                        <!-- Birth to 2 Months -->
                        <div class="vaccine-age-group">
                            <div class="vaccine-age-header">
                                Birth to 2 Months
                            </div>
                            <div class="vaccine-list">
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        Hepatitis B (HepB)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose recommended at birth, second dose at 1-2 months. Protects against hepatitis B virus infection, which can lead to liver disease.
                                        </p>
                                        <form class="reminder-form" data-vaccine="HepB">
                                            <label for="hepbReminder">Set reminder:</label>
                                            <input type="date" id="hepbReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 2 Months -->
                        <div class="vaccine-age-group">
                            <div class="vaccine-age-header">
                                2 Months
                            </div>
                            <div class="vaccine-list">
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        DTaP (Diphtheria, Tetanus, Pertussis)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against diphtheria, tetanus (lockjaw), and pertussis (whooping cough).
                                        </p>
                                        <form class="reminder-form" data-vaccine="DTaP-1">
                                            <label for="dtapReminder">Set reminder:</label>
                                            <input type="date" id="dtapReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        IPV (Polio)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against poliomyelitis, which can cause permanent paralysis.
                                        </p>
                                        <form class="reminder-form" data-vaccine="IPV-1">
                                            <label for="ipvReminder">Set reminder:</label>
                                            <input type="date" id="ipvReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        Hib (Haemophilus influenzae type b)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against Haemophilus influenzae type b, which can cause meningitis and other serious infections.
                                        </p>
                                        <form class="reminder-form" data-vaccine="Hib-1">
                                            <label for="hibReminder">Set reminder:</label>
                                            <input type="date" id="hibReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        PCV13 (Pneumococcal conjugate)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against pneumococcal disease, which can cause ear infections, pneumonia, and meningitis.
                                        </p>
                                        <form class="reminder-form" data-vaccine="PCV13-1">
                                            <label for="pcvReminder">Set reminder:</label>
                                            <input type="date" id="pcvReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        RV (Rotavirus)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against rotavirus, which causes severe diarrhea and vomiting.
                                        </p>
                                        <form class="reminder-form" data-vaccine="RV-1">
                                            <label for="rvReminder">Set reminder:</label>
                                            <input type="date" id="rvReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 4 Months -->
                        <div class="vaccine-age-group">
                            <div class="vaccine-age-header">
                                4 Months
                            </div>
                            <div class="vaccine-list">
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        DTaP (Diphtheria, Tetanus, Pertussis) - 2nd dose
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            Second dose at 4 months. Continues to build protection against diphtheria, tetanus, and pertussis.
                                        </p>
                                        <form class="reminder-form" data-vaccine="DTaP-2">
                                            <label for="dtap2Reminder">Set reminder:</label>
                                            <input type="date" id="dtap2Reminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <!-- Additional 4-month vaccines would be listed here -->
                            </div>
                        </div>

                        <!-- Additional age groups would be added here -->
                    </div>
                </div>
                <p class="disclaimer">
                    Note: This vaccination schedule is based on recommendations from major health organizations. Always consult with your pediatrician for the most appropriate vaccination schedule for your child.
                </p>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="infant-care.html">Infant Care</a></li>
                        <li><a href="tools.html">Tools</a></li>
                        <li><a href="community.html">Community</a></li>
                        <li><a href="blog.html">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="#due-date">Due Date Calculator</a></li>
                        <li><a href="#bmi">BMI Tracker</a></li>
                        <li><a href="#growth-tracker">Growth Tracker</a></li>
                        <li><a href="#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <!-- The main functionality is now handled in main.js -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Clear any conflicting localStorage data
            if (localStorage.getItem('growthData')) {
                // Transfer data from old key to new key if needed
                if (!localStorage.getItem('growthTrackerData')) {
                    localStorage.setItem('growthTrackerData', localStorage.getItem('growthData'));
                }
                // Remove old data
                localStorage.removeItem('growthData');
            }
            // Growth Tracker functionality
            const growthTrackerForm = document.getElementById('growthTrackerForm');
            const growthTableBody = document.getElementById('growthTableBody');
            const chartTypeSelect = document.getElementById('chartType');
            const chartPeriodSelect = document.getElementById('chartPeriod');

            // Initialize growth data array from localStorage or empty array if none exists
            let growthData = JSON.parse(localStorage.getItem('growthData')) || [];

            // Initialize the chart
            let growthChart;

            // Function to update the growth table
            function updateGrowthTable() {
                // Clear the table
                growthTableBody.innerHTML = '';

                if (growthData.length === 0) {
                    growthTableBody.innerHTML = '<tr><td colspan="5" style="text-align: center;">No data available yet</td></tr>';
                    return;
                }

                // Sort data by date (newest first)
                const sortedData = [...growthData].sort((a, b) => new Date(b.date) - new Date(a.date));

                // Add each measurement to the table
                sortedData.forEach(measurement => {
                    const row = document.createElement('tr');

                    // Format the date
                    const date = new Date(measurement.date);
                    const formattedDate = date.toLocaleDateString();

                    // Create table cells
                    row.innerHTML = `
                        <td>${formattedDate}</td>
                        <td>${measurement.age}</td>
                        <td>${measurement.weight} ${measurement.weightUnit}</td>
                        <td>${measurement.height} ${measurement.heightUnit}</td>
                        <td>${measurement.headCircumference ? measurement.headCircumference + ' cm' : '-'}</td>
                    `;

                    growthTableBody.appendChild(row);
                });
            }

            // Function to update the chart
            function updateChart() {
                const chartType = chartTypeSelect.value;
                const chartPeriod = chartPeriodSelect.value;

                // If no data, don't update chart
                if (growthData.length === 0) {
                    return;
                }

                // Sort data by date (oldest first for chart)
                const sortedData = [...growthData].sort((a, b) => new Date(a.date) - new Date(b.date));

                // Filter data based on selected period
                let filteredData = sortedData;
                if (chartPeriod !== 'all') {
                    const now = new Date();
                    const monthsAgo = chartPeriod === '3months' ? 3 : 6;
                    const cutoffDate = new Date(now.setMonth(now.getMonth() - monthsAgo));
                    filteredData = sortedData.filter(item => new Date(item.date) >= cutoffDate);
                }

                // Prepare data for the chart
                const labels = filteredData.map(item => {
                    const date = new Date(item.date);
                    return date.toLocaleDateString();
                });

                let dataValues;
                let yAxisLabel;

                // Set data values and y-axis label based on chart type
                switch (chartType) {
                    case 'weight':
                        // Convert all weights to kg for consistency
                        dataValues = filteredData.map(item => {
                            if (item.weightUnit === 'lb') {
                                return parseFloat((item.weight * 0.453592).toFixed(2)); // Convert lb to kg
                            }
                            return parseFloat(item.weight);
                        });
                        yAxisLabel = 'Weight (kg)';
                        break;
                    case 'height':
                        // Convert all heights to cm for consistency
                        dataValues = filteredData.map(item => {
                            if (item.heightUnit === 'in') {
                                return parseFloat((item.height * 2.54).toFixed(1)); // Convert inches to cm
                            }
                            return parseFloat(item.height);
                        });
                        yAxisLabel = 'Height (cm)';
                        break;
                    case 'head':
                        dataValues = filteredData.map(item => parseFloat(item.headCircumference || 0));
                        yAxisLabel = 'Head Circumference (cm)';
                        break;
                }

                // Destroy previous chart if it exists
                if (growthChart) {
                    growthChart.destroy();
                }

                // Get the canvas element
                const ctx = document.getElementById('growthChart').getContext('2d');

                // Create the chart
                growthChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: yAxisLabel,
                            data: dataValues,
                            backgroundColor: 'rgba(92, 107, 192, 0.2)',
                            borderColor: 'rgba(92, 107, 192, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(92, 107, 192, 1)',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 1,
                            pointRadius: 5,
                            pointHoverRadius: 7,
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                title: {
                                    display: true,
                                    text: yAxisLabel
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: `Baby's ${chartType === 'weight' ? 'Weight' : chartType === 'height' ? 'Height' : 'Head Circumference'} Over Time`,
                                font: {
                                    size: 16
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const index = context.dataIndex;
                                        const measurement = filteredData[index];
                                        let label = '';

                                        switch (chartType) {
                                            case 'weight':
                                                label = `Weight: ${measurement.weight} ${measurement.weightUnit}`;
                                                break;
                                            case 'height':
                                                label = `Height: ${measurement.height} ${measurement.heightUnit}`;
                                                break;
                                            case 'head':
                                                label = `Head Circumference: ${measurement.headCircumference} cm`;
                                                break;
                                        }

                                        return [
                                            `Age: ${measurement.age}`,
                                            label
                                        ];
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Handle form submission
            growthTrackerForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form values
                const date = document.getElementById('measurementDate').value;
                const age = document.getElementById('babyAge').value;
                const weight = parseFloat(document.getElementById('babyWeight').value);
                const weightUnit = document.getElementById('weightUnit').value;
                const height = parseFloat(document.getElementById('babyHeight').value);
                const heightUnit = document.getElementById('heightUnit').value;
                const headCircumference = document.getElementById('headCircumference').value ?
                    parseFloat(document.getElementById('headCircumference').value) : null;

                // Create measurement object
                const measurement = {
                    id: Date.now(), // Unique ID for potential editing/deletion later
                    date,
                    age,
                    weight,
                    weightUnit,
                    height,
                    heightUnit,
                    headCircumference
                };

                // Add to growth data array
                growthData.push(measurement);

                // Save to localStorage
                localStorage.setItem('growthData', JSON.stringify(growthData));

                // Update the table and chart
                updateGrowthTable();
                updateChart();

                // Reset form
                growthTrackerForm.reset();

                // Show success message
                alert('Measurement saved successfully!');
            });

            // Handle chart type and period changes
            chartTypeSelect.addEventListener('change', updateChart);
            chartPeriodSelect.addEventListener('change', updateChart);

            // Initialize table and chart on page load
            updateGrowthTable();

            // Only initialize chart if there's data
            if (growthData.length > 0) {
                updateChart();
            }

            // Due Date Calculator functionality
            const dueDateCalculator = document.getElementById('dueDateCalculator');
            const dueDateResult = document.getElementById('dueDateResult');

            dueDateCalculator.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get LMP date
                const lmpDate = new Date(document.getElementById('lmpDate').value);

                // Calculate due date (LMP + 280 days)
                const dueDate = new Date(lmpDate);
                dueDate.setDate(lmpDate.getDate() + 280);

                // Calculate current pregnancy week
                const today = new Date();
                const pregnancyDays = Math.floor((today - lmpDate) / (1000 * 60 * 60 * 24));
                const pregnancyWeeks = Math.floor(pregnancyDays / 7);
                const remainingDays = pregnancyDays % 7;

                // Determine trimester
                let trimester;
                if (pregnancyWeeks < 0) {
                    trimester = "You haven't reached your LMP date yet.";
                } else if (pregnancyWeeks < 13) {
                    trimester = "You're in your first trimester.";
                } else if (pregnancyWeeks < 27) {
                    trimester = "You're in your second trimester.";
                } else if (pregnancyWeeks < 40) {
                    trimester = "You're in your third trimester.";
                } else {
                    trimester = "You've passed your due date.";
                }

                // Format due date
                const formattedDueDate = dueDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                // Display result
                dueDateResult.innerHTML = `
                    <div class="result-card">
                        <h3>Your Estimated Due Date</h3>
                        <div class="due-date">${formattedDueDate}</div>
                        <div class="pregnancy-week">
                            ${pregnancyWeeks >= 0 ?
                                `You are ${pregnancyWeeks} weeks and ${remainingDays} days pregnant.` :
                                'Your pregnancy has not started yet based on the date provided.'}
                        </div>
                        <div class="trimester-info">
                            <p>${trimester}</p>
                            ${pregnancyWeeks >= 0 && pregnancyWeeks < 40 ?
                                `<p>You have approximately ${40 - pregnancyWeeks} weeks until your due date.</p>` : ''}
                        </div>
                    </div>
                `;
            });

            // BMI Calculator functionality
            const bmiCalculator = document.getElementById('bmiCalculator');
            const bmiResult = document.getElementById('bmiResult');

            bmiCalculator.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get weight and height
                const weight = parseFloat(document.getElementById('weight').value);
                const height = parseFloat(document.getElementById('height').value) / 100; // Convert cm to m

                // Calculate BMI
                const bmi = weight / (height * height);

                // Determine BMI category
                let category, advice;
                if (bmi < 18.5) {
                    category = 'underweight';
                    advice = 'Being underweight during pregnancy may increase the risk of having a low birth weight baby. Consider consulting with your healthcare provider about a nutrition plan to gain appropriate weight.';
                } else if (bmi < 25) {
                    category = 'normal';
                    advice = 'Your BMI is within the normal range. Aim for a weight gain of 25-35 pounds (11.5-16 kg) during pregnancy, or as advised by your healthcare provider.';
                } else if (bmi < 30) {
                    category = 'overweight';
                    advice = 'Being overweight during pregnancy may increase certain risks. Aim for a weight gain of 15-25 pounds (7-11.5 kg) during pregnancy, or as advised by your healthcare provider.';
                } else {
                    category = 'obese';
                    advice = 'Obesity during pregnancy may increase risks for complications. Aim for a weight gain of 11-20 pounds (5-9 kg) during pregnancy, or as advised by your healthcare provider.';
                }

                // Display result
                bmiResult.innerHTML = `
                    <div class="result-card">
                        <h3>Your BMI Result</h3>
                        <div class="bmi-value">${bmi.toFixed(1)}</div>
                        <div class="bmi-category ${category}">
                            Category: ${category.charAt(0).toUpperCase() + category.slice(1)}
                        </div>
                        <div class="bmi-advice">
                            <p>${advice}</p>
                            <p>Remember that BMI is just one measure of health. Always consult with your healthcare provider for personalized advice.</p>
                        </div>
                    </div>
                `;
            });

            // Vaccination Schedule functionality
            const vaccineToggles = document.querySelectorAll('.vaccine-toggle');

            vaccineToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const details = this.parentElement.nextElementSibling;
                    const icon = this.querySelector('i');

                    if (details.style.maxHeight) {
                        details.style.maxHeight = null;
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    } else {
                        details.style.maxHeight = details.scrollHeight + 'px';
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    }
                });
            });

            // Reminder form functionality
            const reminderForms = document.querySelectorAll('.reminder-form');

            reminderForms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const vaccine = this.getAttribute('data-vaccine');
                    const reminderDate = this.querySelector('input[type="date"]').value;
                    const statusElement = this.nextElementSibling;

                    // Save reminder to localStorage
                    const reminders = JSON.parse(localStorage.getItem('vaccineReminders')) || {};
                    reminders[vaccine] = reminderDate;
                    localStorage.setItem('vaccineReminders', JSON.stringify(reminders));

                    // Show confirmation
                    statusElement.textContent = `Reminder set for ${new Date(reminderDate).toLocaleDateString()}`;
                    statusElement.style.display = 'block';

                    // Clear after 3 seconds
                    setTimeout(() => {
                        statusElement.style.display = 'none';
                    }, 3000);
                });
            });

            // Load existing reminders
            const savedReminders = JSON.parse(localStorage.getItem('vaccineReminders')) || {};

            reminderForms.forEach(form => {
                const vaccine = form.getAttribute('data-vaccine');
                const dateInput = form.querySelector('input[type="date"]');
                const statusElement = form.nextElementSibling;

                if (savedReminders[vaccine]) {
                    dateInput.value = savedReminders[vaccine];
                    statusElement.textContent = `Reminder set for ${new Date(savedReminders[vaccine]).toLocaleDateString()}`;
                    statusElement.style.display = 'block';
                }
            });
        });
    </script>
</body>
</html>
