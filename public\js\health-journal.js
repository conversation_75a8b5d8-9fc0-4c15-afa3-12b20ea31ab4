/**
 * Health Journal functionality for MATERNIFY
 * This script handles the health journal features including adding, editing, and deleting entries
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Health Journal script loaded');

    // DOM Elements
    const journalForm = document.getElementById('journalForm');
    const healthJournalForm = document.getElementById('healthJournalForm');
    const journalEntries = document.getElementById('journalEntries');
    const noEntries = document.getElementById('noEntries');
    const newEntryBtn = document.getElementById('newEntryBtn');
    const startJournalBtn = document.getElementById('startJournalBtn');
    const cancelEntryBtn = document.getElementById('cancelEntryBtn');
    const exportJournalBtn = document.getElementById('exportJournalBtn');
    const journalTabs = document.querySelectorAll('.journal-tab');
    
    // Current state
    let currentTab = 'all';
    let editingEntryId = null;
    
    // Initialize journal entries from localStorage
    let journalData = JSON.parse(localStorage.getItem('healthJournal')) || [];
    
    // Set today's date as default for new entries
    document.getElementById('entryDate').valueAsDate = new Date();
    
    // Event Listeners
    newEntryBtn.addEventListener('click', showJournalForm);
    startJournalBtn.addEventListener('click', showJournalForm);
    cancelEntryBtn.addEventListener('click', hideJournalForm);
    healthJournalForm.addEventListener('submit', saveJournalEntry);
    exportJournalBtn.addEventListener('click', exportJournal);
    
    // Tab switching
    journalTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Update active tab
            journalTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Update current tab and refresh entries
            currentTab = this.getAttribute('data-tab');
            displayJournalEntries();
        });
    });
    
    // Initialize the journal display
    displayJournalEntries();
    
    /**
     * Display journal entries based on the selected tab
     */
    function displayJournalEntries() {
        // Filter entries based on selected tab
        let filteredEntries = journalData;
        if (currentTab !== 'all') {
            filteredEntries = journalData.filter(entry => entry.category === currentTab);
        }
        
        // Sort entries by date (newest first)
        filteredEntries.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        // Clear the entries container
        journalEntries.innerHTML = '';
        
        // Show "no entries" message if there are no entries
        if (filteredEntries.length === 0) {
            if (journalData.length === 0) {
                noEntries.style.display = 'block';
            } else {
                journalEntries.innerHTML = `
                    <div class="no-entries">
                        <h3>No entries in this category</h3>
                        <p>Switch to a different category or add a new entry.</p>
                    </div>
                `;
            }
            return;
        }
        
        // Hide "no entries" message
        noEntries.style.display = 'none';
        
        // Create HTML for each entry
        filteredEntries.forEach(entry => {
            const entryElement = document.createElement('div');
            entryElement.className = 'journal-entry';
            entryElement.innerHTML = `
                <div class="journal-entry-header">
                    <span class="journal-entry-date">${formatDate(entry.date)}</span>
                    <span class="journal-entry-category">${formatCategory(entry.category)}</span>
                </div>
                <h3 class="journal-entry-title">${entry.title}</h3>
                <div class="journal-entry-content">${formatContent(entry.content)}</div>
                <div class="journal-entry-actions">
                    <button class="edit-btn" data-id="${entry.id}"><i class="fas fa-edit"></i> Edit</button>
                    <button class="delete-btn" data-id="${entry.id}"><i class="fas fa-trash-alt"></i> Delete</button>
                </div>
            `;
            
            journalEntries.appendChild(entryElement);
        });
        
        // Add event listeners to edit and delete buttons
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const entryId = this.getAttribute('data-id');
                editEntry(entryId);
            });
        });
        
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const entryId = this.getAttribute('data-id');
                deleteEntry(entryId);
            });
        });
    }
    
    /**
     * Show the journal entry form
     */
    function showJournalForm() {
        journalForm.style.display = 'block';
        noEntries.style.display = 'none';
        document.getElementById('entryDate').valueAsDate = new Date();
        document.getElementById('entryCategory').value = currentTab !== 'all' ? currentTab : '';
        document.getElementById('entryTitle').value = '';
        document.getElementById('entryContent').value = '';
        document.getElementById('entryTitle').focus();
    }
    
    /**
     * Hide the journal entry form
     */
    function hideJournalForm() {
        journalForm.style.display = 'none';
        editingEntryId = null;
        
        // Show "no entries" message if there are no entries
        if (journalData.length === 0) {
            noEntries.style.display = 'block';
        }
    }
    
    /**
     * Save a journal entry (new or edited)
     */
    function saveJournalEntry(e) {
        e.preventDefault();
        
        const entryDate = document.getElementById('entryDate').value;
        const entryCategory = document.getElementById('entryCategory').value;
        const entryTitle = document.getElementById('entryTitle').value;
        const entryContent = document.getElementById('entryContent').value;
        
        if (editingEntryId) {
            // Update existing entry
            const index = journalData.findIndex(entry => entry.id === editingEntryId);
            if (index !== -1) {
                journalData[index] = {
                    id: editingEntryId,
                    date: entryDate,
                    category: entryCategory,
                    title: entryTitle,
                    content: entryContent
                };
            }
            editingEntryId = null;
        } else {
            // Create new entry
            const newEntry = {
                id: Date.now().toString(),
                date: entryDate,
                category: entryCategory,
                title: entryTitle,
                content: entryContent
            };
            
            journalData.push(newEntry);
        }
        
        // Save to localStorage
        localStorage.setItem('healthJournal', JSON.stringify(journalData));
        
        // Hide form and refresh entries
        hideJournalForm();
        displayJournalEntries();
        
        // Show success message
        alert('Journal entry saved successfully!');
    }
    
    /**
     * Edit an existing journal entry
     */
    function editEntry(entryId) {
        const entry = journalData.find(entry => entry.id === entryId);
        if (!entry) return;
        
        // Set form values
        document.getElementById('entryDate').value = entry.date;
        document.getElementById('entryCategory').value = entry.category;
        document.getElementById('entryTitle').value = entry.title;
        document.getElementById('entryContent').value = entry.content;
        
        // Set editing state
        editingEntryId = entryId;
        
        // Show form
        journalForm.style.display = 'block';
        noEntries.style.display = 'none';
        document.getElementById('entryTitle').focus();
    }
    
    /**
     * Delete a journal entry
     */
    function deleteEntry(entryId) {
        if (!confirm('Are you sure you want to delete this entry? This action cannot be undone.')) {
            return;
        }
        
        // Remove entry from array
        journalData = journalData.filter(entry => entry.id !== entryId);
        
        // Save to localStorage
        localStorage.setItem('healthJournal', JSON.stringify(journalData));
        
        // Refresh entries
        displayJournalEntries();
        
        // Show success message
        alert('Journal entry deleted successfully!');
    }
    
    /**
     * Export journal entries as a text file
     */
    function exportJournal() {
        if (journalData.length === 0) {
            alert('No journal entries to export.');
            return;
        }
        
        let exportText = 'MATERNIFY HEALTH JOURNAL\n';
        exportText += '=======================\n\n';
        
        // Sort entries by date (newest first)
        const sortedEntries = [...journalData].sort((a, b) => new Date(b.date) - new Date(a.date));
        
        // Add each entry to the export text
        sortedEntries.forEach(entry => {
            exportText += `Date: ${formatDate(entry.date)}\n`;
            exportText += `Category: ${formatCategory(entry.category)}\n`;
            exportText += `Title: ${entry.title}\n`;
            exportText += `Content: ${entry.content}\n`;
            exportText += '-------------------------------------------\n\n';
        });
        
        // Create a download link
        const blob = new Blob([exportText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'maternify-health-journal.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    /**
     * Format a date string to a more readable format
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    }
    
    /**
     * Format category name for display
     */
    function formatCategory(category) {
        return category.charAt(0).toUpperCase() + category.slice(1);
    }
    
    /**
     * Format content for display (convert newlines to <br> tags)
     */
    function formatContent(content) {
        return content.replace(/\n/g, '<br>');
    }
});
