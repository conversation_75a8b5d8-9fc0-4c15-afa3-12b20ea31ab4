// Tools controller for MATERNIFY

// Due date calculator controller
exports.calculateDueDate = (req, res) => {
    const { lmpDate } = req.body;
    
    if (!lmpDate) {
        return res.status(400).json({ success: false, message: 'Last menstrual period date is required' });
    }
    
    // Calculate due date (280 days from LMP)
    const lmp = new Date(lmpDate);
    const dueDate = new Date(lmp);
    dueDate.setDate(lmp.getDate() + 280);
    
    // Calculate current pregnancy week
    const today = new Date();
    const pregnancyDays = Math.floor((today - lmp) / (1000 * 60 * 60 * 24));
    const pregnancyWeeks = Math.floor(pregnancyDays / 7);
    const remainingDays = pregnancyDays % 7;
    
    let weekText = '';
    if (pregnancyDays < 0) {
        weekText = 'Invalid date: Last menstrual period date cannot be in the future.';
    } else if (pregnancyWeeks > 42) {
        weekText = 'It appears your due date has passed. Please consult with your healthcare provider.';
    } else {
        weekText = `You are currently ${pregnancyWeeks} weeks and ${remainingDays} days pregnant.`;
    }
    
    res.json({
        success: true,
        dueDate: dueDate.toISOString().split('T')[0],
        pregnancyWeeks,
        remainingDays,
        weekText
    });
};

// BMI calculator controller
exports.calculateBMI = (req, res) => {
    const { weight, height, unit } = req.body;
    
    if (!weight || !height) {
        return res.status(400).json({ success: false, message: 'Weight and height are required' });
    }
    
    let bmi;
    if (unit === 'metric') {
        // Metric: BMI = weight(kg) / height(m)²
        bmi = weight / ((height / 100) * (height / 100));
    } else {
        // Imperial: BMI = (weight(lb) / height(in)²) * 703
        bmi = (weight / (height * height)) * 703;
    }
    
    let category, advice;
    
    if (bmi < 18.5) {
        category = 'Underweight';
        advice = 'You may need to gain weight. Consult with your healthcare provider about a healthy diet plan.';
    } else if (bmi >= 18.5 && bmi < 25) {
        category = 'Normal weight';
        advice = 'You are at a healthy weight. Continue to maintain a balanced diet and regular exercise.';
    } else if (bmi >= 25 && bmi < 30) {
        category = 'Overweight';
        advice = 'Consider discussing a healthy weight management plan with your healthcare provider.';
    } else {
        category = 'Obese';
        advice = 'Please consult with your healthcare provider about weight management strategies.';
    }
    
    res.json({
        success: true,
        bmi: bmi.toFixed(1),
        category,
        advice
    });
};
