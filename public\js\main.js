document.addEventListener('DOMContentLoaded', function() {
    // Mobile Menu Toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu');

    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenuBtn.classList.toggle('active');
            navMenu.classList.toggle('active');

            // Toggle hamburger menu animation
            const bars = mobileMenuBtn.querySelectorAll('.bar');
            if (mobileMenuBtn.classList.contains('active')) {
                bars[0].style.transform = 'rotate(-45deg) translate(-5px, 6px)';
                bars[1].style.opacity = '0';
                bars[2].style.transform = 'rotate(45deg) translate(-5px, -6px)';
            } else {
                bars[0].style.transform = 'none';
                bars[1].style.opacity = '1';
                bars[2].style.transform = 'none';
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navMenu.contains(event.target) && !mobileMenuBtn.contains(event.target) && navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                mobileMenuBtn.classList.remove('active');

                const bars = mobileMenuBtn.querySelectorAll('.bar');
                bars[0].style.transform = 'none';
                bars[1].style.opacity = '1';
                bars[2].style.transform = 'none';
            }
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            if (this.getAttribute('href') !== '#') {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    window.scrollTo({
                        top: target.offsetTop - 80,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (navMenu && navMenu.classList.contains('active')) {
                        navMenu.classList.remove('active');
                        if (mobileMenuBtn) {
                            mobileMenuBtn.classList.remove('active');
                            const bars = mobileMenuBtn.querySelectorAll('.bar');
                            bars[0].style.transform = 'none';
                            bars[1].style.opacity = '1';
                            bars[2].style.transform = 'none';
                        }
                    }
                }
            }
        });
    });

    // Newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const emailInput = this.querySelector('input[type="email"]');
            if (emailInput && emailInput.value) {
                try {
                    // Check if API service is available
                    if (!window.apiService) {
                        console.error('API service not found. Make sure api-service.js is loaded before this script');
                        alert('An error occurred. Please try again later.');
                        return;
                    }

                    // Call the subscribe API using the API service
                    const result = await apiService.subscribeNewsletter(emailInput.value);

                    if (result.success) {
                        alert('Thank you for subscribing to our newsletter!');
                        emailInput.value = '';
                    } else {
                        alert(result.message || 'An error occurred. Please try again.');
                    }
                } catch (error) {
                    console.error('Newsletter subscription error:', error);
                    alert('An error occurred during subscription. Please try again.');
                }
            }
        });
    }

    // Add active class to current page in navigation
    const currentLocation = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentLocation ||
            (currentLocation.includes(link.getAttribute('href')) && link.getAttribute('href') !== 'index.html')) {
            link.classList.add('active');
        }
    });

    // Password visibility toggle
    const passwordToggles = document.querySelectorAll('.toggle-password');
    if (passwordToggles) {
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const passwordField = this.previousElementSibling;
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);
                this.innerHTML = type === 'password' ? '<i class="far fa-eye"></i>' : '<i class="far fa-eye-slash"></i>';
            });
        });
    }

    // Form validation
    const validateForm = (form, fields) => {
        let isValid = true;

        fields.forEach(field => {
            const input = form.querySelector(`#${field.id}`);
            const errorElement = form.querySelector(`#${field.id}Error`);

            if (!input || !errorElement) return;

            let fieldIsValid = true;
            let errorMessage = '';

            // Required field validation
            if (field.required && input.value.trim() === '') {
                fieldIsValid = false;
                errorMessage = field.requiredMessage || 'This field is required';
            }

            // Email validation
            if (field.type === 'email' && input.value.trim() !== '') {
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailPattern.test(input.value)) {
                    fieldIsValid = false;
                    errorMessage = field.typeMessage || 'Please enter a valid email address';
                }
            }

            // Password validation
            if (field.type === 'password' && input.value.trim() !== '') {
                const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/;
                if (!passwordPattern.test(input.value)) {
                    fieldIsValid = false;
                    errorMessage = field.typeMessage || 'Password must be at least 8 characters with letters and numbers';
                }
            }

            // Password confirmation validation
            if (field.type === 'passwordConfirm') {
                const passwordInput = form.querySelector(`#${field.matchWith}`);
                if (passwordInput && input.value !== passwordInput.value) {
                    fieldIsValid = false;
                    errorMessage = field.typeMessage || 'Passwords do not match';
                }
            }

            // Custom validation
            if (field.validate && typeof field.validate === 'function') {
                const customValidation = field.validate(input.value);
                if (customValidation !== true) {
                    fieldIsValid = false;
                    errorMessage = customValidation;
                }
            }

            // Update UI based on validation
            if (!fieldIsValid) {
                errorElement.textContent = errorMessage;
                errorElement.style.display = 'block';
                input.style.borderColor = 'var(--error-color)';
                isValid = false;
            } else {
                errorElement.style.display = 'none';
                input.style.borderColor = '';
            }
        });

        return isValid;
    };

    // Login form validation
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const fields = [
                {
                    id: 'email',
                    required: true,
                    requiredMessage: 'Please enter your email address',
                    type: 'email',
                    typeMessage: 'Please enter a valid email address'
                },
                {
                    id: 'password',
                    required: true,
                    requiredMessage: 'Please enter your password'
                }
            ];

            if (validateForm(this, fields)) {
                // In a real application, this would authenticate with the server
                alert('Login successful! Redirecting to your dashboard.');
                window.location.href = '/dashboard';
            }
        });
    }

    // Registration form validation
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const fields = [
                {
                    id: 'firstName',
                    required: true,
                    requiredMessage: 'Please enter your first name'
                },
                {
                    id: 'lastName',
                    required: true,
                    requiredMessage: 'Please enter your last name'
                },
                {
                    id: 'email',
                    required: true,
                    requiredMessage: 'Please enter your email address',
                    type: 'email',
                    typeMessage: 'Please enter a valid email address'
                },
                {
                    id: 'password',
                    required: true,
                    requiredMessage: 'Please enter a password',
                    type: 'password',
                    typeMessage: 'Password must be at least 8 characters with letters and numbers'
                },
                {
                    id: 'confirmPassword',
                    required: true,
                    requiredMessage: 'Please confirm your password',
                    type: 'passwordConfirm',
                    matchWith: 'password',
                    typeMessage: 'Passwords do not match'
                }
            ];

            // Terms agreement validation
            const termsAgree = document.getElementById('termsAgree');
            let termsValid = true;

            if (termsAgree && !termsAgree.checked) {
                termsValid = false;
                termsAgree.nextElementSibling.style.color = 'var(--error-color)';
            } else if (termsAgree) {
                termsAgree.nextElementSibling.style.color = '';
            }

            if (validateForm(this, fields) && termsValid) {
                // In a real application, this would send data to the server
                alert('Registration successful! You can now log in.');
                window.location.href = '/login?registered=true&social=google';
            }
        });
    }

    // Due Date Calculator
    const dueDateCalculator = document.getElementById('dueDateCalculator');
    if (dueDateCalculator) {
        dueDateCalculator.addEventListener('submit', function(e) {
            e.preventDefault();

            const lmpDate = new Date(document.getElementById('lmpDate').value);
            const resultElement = document.getElementById('dueDateResult');

            if (isNaN(lmpDate.getTime())) {
                resultElement.innerHTML = '<div class="error-message">Please enter a valid date</div>';
                return;
            }

            // Calculate due date (LMP + 280 days)
            const dueDate = new Date(lmpDate);
            dueDate.setDate(lmpDate.getDate() + 280);

            // Format the date
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            const formattedDate = dueDate.toLocaleDateString('en-US', options);

            // Calculate current pregnancy week
            const today = new Date();
            const pregnancyDays = Math.floor((today - lmpDate) / (1000 * 60 * 60 * 24));
            const pregnancyWeeks = Math.floor(pregnancyDays / 7);
            const remainingDays = pregnancyDays % 7;

            let weekText = '';
            if (pregnancyDays < 0) {
                weekText = 'Invalid date: Last menstrual period date cannot be in the future.';
            } else if (pregnancyWeeks > 42) {
                weekText = 'It appears your due date has passed. Please consult with your healthcare provider.';
            } else {
                weekText = `You are currently ${pregnancyWeeks} weeks and ${remainingDays} days pregnant.`;
            }

            resultElement.innerHTML = `
                <div class="result-card">
                    <h3>Your Estimated Due Date</h3>
                    <p class="due-date">${formattedDate}</p>
                    <p class="pregnancy-week">${weekText}</p>
                    <div class="trimester-info">
                        <p>First Trimester: Weeks 1-12</p>
                        <p>Second Trimester: Weeks 13-26</p>
                        <p>Third Trimester: Weeks 27-40</p>
                    </div>
                </div>
            `;
        });
    }

    // BMI Calculator
    const bmiCalculator = document.getElementById('bmiCalculator');
    if (bmiCalculator) {
        bmiCalculator.addEventListener('submit', function(e) {
            e.preventDefault();

            const weight = parseFloat(document.getElementById('weight').value);
            const height = parseFloat(document.getElementById('height').value) / 100; // Convert cm to m
            const resultElement = document.getElementById('bmiResult');

            if (isNaN(weight) || isNaN(height) || height <= 0 || weight <= 0) {
                resultElement.innerHTML = '<div class="error-message">Please enter valid values for weight and height</div>';
                return;
            }

            // Calculate BMI
            const bmi = weight / (height * height);
            const roundedBmi = Math.round(bmi * 10) / 10;

            // Determine BMI category
            let category, advice, categoryClass;

            if (bmi < 18.5) {
                category = 'Underweight';
                advice = 'Consider consulting with your healthcare provider about healthy weight gain during pregnancy.';
                categoryClass = 'underweight';
            } else if (bmi >= 18.5 && bmi < 25) {
                category = 'Normal weight';
                advice = 'Maintain a balanced diet and regular exercise as recommended by your healthcare provider.';
                categoryClass = 'normal';
            } else if (bmi >= 25 && bmi < 30) {
                category = 'Overweight';
                advice = 'Focus on nutrient-dense foods and appropriate exercise. Consult your healthcare provider for personalized advice.';
                categoryClass = 'overweight';
            } else {
                category = 'Obese';
                advice = 'Work with your healthcare provider to develop a safe pregnancy plan that includes nutrition and appropriate physical activity.';
                categoryClass = 'obese';
            }

            resultElement.innerHTML = `
                <div class="result-card">
                    <h3>Your BMI Result</h3>
                    <p class="bmi-value">${roundedBmi}</p>
                    <p class="bmi-category ${categoryClass}">${category}</p>
                    <div class="bmi-advice">
                        <p>${advice}</p>
                        <p class="disclaimer">Note: BMI is just one health indicator. Always consult with your healthcare provider for personalized advice during pregnancy.</p>
                    </div>
                </div>
            `;
        });
    }

    // Growth Tracker
    const growthForm = document.getElementById('growthTrackerForm');
    if (growthForm) {
        // Clean up any invalid data in localStorage
        const cleanupInvalidData = () => {
            const savedData = localStorage.getItem('growthTrackerData');
            if (savedData) {
                try {
                    let data = JSON.parse(savedData);

                    // Filter out entries with invalid data
                    const cleanData = data.filter(entry => {
                        // Check if weight and height are valid numbers
                        const validWeight = entry.weight !== undefined && entry.weight !== null && !isNaN(parseFloat(entry.weight));
                        const validHeight = entry.height !== undefined && entry.height !== null && !isNaN(parseFloat(entry.height));

                        // Keep only entries with valid weight and height
                        return validWeight && validHeight;
                    });

                    // Convert string values to numbers
                    cleanData.forEach(entry => {
                        if (entry.weight !== undefined && entry.weight !== null) {
                            entry.weight = parseFloat(entry.weight);
                        }
                        if (entry.height !== undefined && entry.height !== null) {
                            entry.height = parseFloat(entry.height);
                        }
                        if (entry.headCircumference !== undefined && entry.headCircumference !== null && entry.headCircumference !== '') {
                            entry.headCircumference = parseFloat(entry.headCircumference);
                        }
                    });

                    // Save cleaned data back to localStorage
                    localStorage.setItem('growthTrackerData', JSON.stringify(cleanData));
                    return cleanData;
                } catch (e) {
                    console.error('Error cleaning up growth tracker data:', e);
                    localStorage.removeItem('growthTrackerData');
                    return [];
                }
            }
            return [];
        };

        // Load saved data if available
        const loadSavedData = () => {
            const data = cleanupInvalidData();
            if (data.length > 0) {
                displayGrowthData(data);
            }
        };

        // Display growth data in table and chart
        const displayGrowthData = (data) => {
            const tableBody = document.getElementById('growthTableBody');
            const chartContainer = document.getElementById('growthChart');

            if (!tableBody || !chartContainer || !data || data.length === 0) return;

            // Clear existing data
            tableBody.innerHTML = '';

            // Sort data by date (handling invalid dates)
            data.sort((a, b) => {
                const dateA = new Date(a.date);
                const dateB = new Date(b.date);

                // Check if dates are valid
                if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) {
                    return 0; // Both invalid, maintain order
                } else if (isNaN(dateA.getTime())) {
                    return 1; // A is invalid, B comes first
                } else if (isNaN(dateB.getTime())) {
                    return -1; // B is invalid, A comes first
                }

                // Both valid, compare normally
                return dateA - dateB;
            });

            // Populate table
            data.forEach((entry, index) => {
                const row = document.createElement('tr');
                row.dataset.index = index; // Store the index for edit/delete operations

                // Format date properly
                const dateCell = document.createElement('td');
                try {
                    const date = new Date(entry.date);
                    if (!isNaN(date.getTime())) {
                        dateCell.textContent = date.toLocaleDateString('en-US');
                    } else {
                        dateCell.textContent = entry.date || 'Unknown Date';
                    }
                } catch (e) {
                    dateCell.textContent = entry.date || 'Unknown Date';
                }

                const ageCell = document.createElement('td');
                ageCell.textContent = entry.age || 'Unknown';

                const weightCell = document.createElement('td');
                if (entry.weight !== undefined && entry.weight !== null && !isNaN(entry.weight)) {
                    weightCell.textContent = `${entry.weight} ${entry.weightUnit || 'kg'}`;
                } else {
                    weightCell.textContent = 'Unknown';
                }

                const heightCell = document.createElement('td');
                if (entry.height !== undefined && entry.height !== null && !isNaN(entry.height)) {
                    heightCell.textContent = `${entry.height} ${entry.heightUnit || 'cm'}`;
                } else {
                    heightCell.textContent = 'Unknown';
                }

                const headCell = document.createElement('td');
                if (entry.headCircumference !== undefined && entry.headCircumference !== null && !isNaN(entry.headCircumference)) {
                    headCell.textContent = `${entry.headCircumference} cm`;
                } else {
                    headCell.textContent = '-';
                }

                // Add actions cell with edit and delete buttons
                const actionsCell = document.createElement('td');
                actionsCell.className = 'actions-cell';

                // Create edit button
                const editBtn = document.createElement('button');
                editBtn.innerHTML = '<i class="fas fa-edit"></i>';
                editBtn.className = 'action-btn edit-btn';
                editBtn.title = 'Edit measurement';
                editBtn.addEventListener('click', function() {
                    // Fill the form with the entry data for editing
                    document.getElementById('measurementDate').value = entry.date;
                    document.getElementById('babyAge').value = entry.age;
                    document.getElementById('babyWeight').value = entry.weight;
                    document.getElementById('weightUnit').value = entry.weightUnit || 'kg';
                    document.getElementById('babyHeight').value = entry.height;
                    document.getElementById('heightUnit').value = entry.heightUnit || 'cm';
                    document.getElementById('headCircumference').value = entry.headCircumference || '';

                    // Change the submit button to update mode
                    const submitBtn = document.querySelector('#growthTrackerForm button[type="submit"]');
                    submitBtn.textContent = 'Update Measurement';
                    submitBtn.dataset.mode = 'update';
                    submitBtn.dataset.index = index;

                    // Scroll to the form
                    document.getElementById('growthTrackerForm').scrollIntoView({ behavior: 'smooth' });
                });

                // Create delete button
                const deleteBtn = document.createElement('button');
                deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
                deleteBtn.className = 'action-btn delete-btn';
                deleteBtn.title = 'Delete measurement';
                deleteBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to delete this measurement? This cannot be undone.')) {
                        // Remove the entry from the data array
                        data.splice(index, 1);

                        // Save updated data to localStorage
                        localStorage.setItem('growthTrackerData', JSON.stringify(data));

                        // Update the display
                        displayGrowthData(data);
                    }
                });

                actionsCell.appendChild(editBtn);
                actionsCell.appendChild(deleteBtn);

                row.appendChild(dateCell);
                row.appendChild(ageCell);
                row.appendChild(weightCell);
                row.appendChild(heightCell);
                row.appendChild(headCell);
                row.appendChild(actionsCell);

                tableBody.appendChild(row);
            });

            // Create chart visualization using Chart.js
            // First, clear any existing chart
            chartContainer.innerHTML = '';

            // Check if we have any valid data to display
            const hasValidData = data.some(entry =>
                (entry.weight !== undefined && !isNaN(parseFloat(entry.weight))) ||
                (entry.height !== undefined && !isNaN(parseFloat(entry.height))) ||
                (entry.headCircumference !== undefined && !isNaN(parseFloat(entry.headCircumference)))
            );

            if (!hasValidData) {
                // If no valid data, show a message
                const noDataMsg = document.createElement('p');
                noDataMsg.textContent = 'Add measurements to see growth charts';
                noDataMsg.style.textAlign = 'center';
                noDataMsg.style.padding = '20px';
                chartContainer.appendChild(noDataMsg);
                return; // Exit the function early
            }

            // Create canvas for the chart
            const canvas = document.createElement('canvas');
            canvas.id = 'growthChartCanvas';
            chartContainer.appendChild(canvas);

            // Prepare data for chart
            const labels = data.map(entry => {
                try {
                    const date = new Date(entry.date);
                    if (!isNaN(date.getTime())) {
                        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                    }
                    return entry.age || 'Unknown';
                } catch (e) {
                    return entry.age || 'Unknown';
                }
            });

            // Convert all data to numbers and handle null/undefined values
            const weightData = data.map(entry => {
                const weight = parseFloat(entry.weight);
                return isNaN(weight) ? null : weight;
            });

            const heightData = data.map(entry => {
                const height = parseFloat(entry.height);
                return isNaN(height) ? null : height;
            });

            const headData = data.map(entry => {
                if (!entry.headCircumference) return null;
                const head = parseFloat(entry.headCircumference);
                return isNaN(head) ? null : head;
            });

            // Get chart type and period from selectors if they exist
            let chartType = 'weight';
            const chartTypeSelect = document.getElementById('chartType');
            if (chartTypeSelect) {
                chartType = chartTypeSelect.value;
            }

            // Create the chart
            const ctx = canvas.getContext('2d');
            let datasets = [];
            let chartTitle = 'Baby Growth Chart';

            // Create datasets based on chart type
            if (chartType === 'all') {
                // Show all measurements in one chart with different colors
                datasets = [
                    {
                        label: 'Weight',
                        data: weightData,
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: false,
                        yAxisID: 'y-weight'
                    },
                    {
                        label: 'Height/Length',
                        data: heightData,
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: false,
                        yAxisID: 'y-height'
                    }
                ];

                // Only add head circumference if we have data
                if (headData.some(value => value !== null)) {
                    datasets.push({
                        label: 'Head Circumference',
                        data: headData,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: false,
                        yAxisID: 'y-head'
                    });
                }
            } else {
                // Show single measurement type
                let chartData, borderColor, backgroundColor, yAxisID;

                switch(chartType) {
                    case 'height':
                        chartData = heightData;
                        chartTitle = 'Height/Length Growth';
                        borderColor = 'rgb(54, 162, 235)';
                        backgroundColor = 'rgba(54, 162, 235, 0.1)';
                        yAxisID = 'y';
                        break;
                    case 'head':
                        chartData = headData;
                        chartTitle = 'Head Circumference Growth';
                        borderColor = 'rgb(75, 192, 192)';
                        backgroundColor = 'rgba(75, 192, 192, 0.1)';
                        yAxisID = 'y';
                        break;
                    case 'weight':
                    default:
                        chartData = weightData;
                        chartTitle = 'Weight Growth';
                        borderColor = 'rgb(255, 99, 132)';
                        backgroundColor = 'rgba(255, 99, 132, 0.1)';
                        yAxisID = 'y';
                        break;
                }

                datasets = [{
                    label: chartTitle,
                    data: chartData,
                    borderColor: borderColor,
                    backgroundColor: backgroundColor,
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    yAxisID: yAxisID
                }];
            }

            // Define scales based on chart type
            let scales = {};

            if (chartType === 'all') {
                scales = {
                    'y-weight': {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Weight'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    },
                    'y-height': {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Height/Length (cm)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date/Age'
                        }
                    }
                };

                // Only add head circumference axis if we have data
                if (headData.some(value => value !== null)) {
                    scales['y-head'] = {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Head Circ. (cm)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    };
                }
            } else {
                scales = {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: chartType === 'weight' ? 'Weight' :
                                  chartType === 'height' ? 'Height/Length (cm)' : 'Head Circumference (cm)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date/Age'
                        }
                    }
                };
            }

            // Create the chart
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: chartTitle,
                            font: {
                                size: 16,
                                weight: 'bold'
                            },
                            padding: {
                                top: 10,
                                bottom: 20
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        const entry = data[context.dataIndex];
                                        if (context.dataset.label === 'Weight') {
                                            label += context.parsed.y + ' ' + (entry.weightUnit || 'kg');
                                        } else if (context.dataset.label === 'Height/Length') {
                                            label += context.parsed.y + ' ' + (entry.heightUnit || 'cm');
                                        } else if (context.dataset.label === 'Head Circumference') {
                                            label += context.parsed.y + ' cm';
                                        } else {
                                            // For single measurement charts
                                            if (chartType === 'weight') {
                                                label += context.parsed.y + ' ' + (entry.weightUnit || 'kg');
                                            } else if (chartType === 'height') {
                                                label += context.parsed.y + ' ' + (entry.heightUnit || 'cm');
                                            } else {
                                                label += context.parsed.y + ' cm';
                                            }
                                        }
                                    }
                                    return label;
                                },
                                title: function(context) {
                                    // Show both date and age in tooltip title
                                    const index = context[0].dataIndex;
                                    const entry = data[index];
                                    let title = labels[index];

                                    if (entry.age) {
                                        title += ' (' + entry.age + ')';
                                    }

                                    return title;
                                }
                            }
                        }
                    },
                    scales: scales,
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    },
                    elements: {
                        point: {
                            radius: 4,
                            hoverRadius: 6
                        }
                    }
                }
            });
        };

        // Add or update growth data
        growthForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const isUpdateMode = submitBtn.dataset.mode === 'update';

            const date = document.getElementById('measurementDate').value;
            const age = document.getElementById('babyAge').value;
            const weight = parseFloat(document.getElementById('babyWeight').value);
            const weightUnit = document.getElementById('weightUnit').value;
            const height = parseFloat(document.getElementById('babyHeight').value);
            const heightUnit = document.getElementById('heightUnit').value;
            const headCircumference = document.getElementById('headCircumference').value ?
                parseFloat(document.getElementById('headCircumference').value) : null;

            if (!date || !age || isNaN(weight) || isNaN(height)) {
                alert('Please fill in all required fields with valid numbers');
                return;
            }

            // Create entry object
            const entry = {
                date,
                age,
                weight,
                weightUnit,
                height,
                heightUnit,
                headCircumference
            };

            // Get existing data or initialize empty array
            let existingData = [];
            const savedData = localStorage.getItem('growthTrackerData');
            if (savedData) {
                existingData = JSON.parse(savedData);
            }

            if (isUpdateMode) {
                // Update existing entry
                const index = parseInt(submitBtn.dataset.index);
                if (!isNaN(index) && index >= 0 && index < existingData.length) {
                    existingData[index] = entry;

                    // Reset the submit button
                    submitBtn.textContent = 'Save Measurement';
                    submitBtn.dataset.mode = 'add';
                    delete submitBtn.dataset.index;

                    // Show success message
                    alert('Measurement updated successfully!');
                } else {
                    alert('Error: Could not find the measurement to update.');
                }
            } else {
                // Add new entry
                existingData.push(entry);

                // Show success message
                alert('Growth data saved successfully!');
            }

            // Save to localStorage
            localStorage.setItem('growthTrackerData', JSON.stringify(existingData));

            // Update display
            displayGrowthData(existingData);

            // Reset form
            growthForm.reset();
        });

        // Add event listener for clear data button
        const clearDataButton = document.getElementById('clearGrowthData');
        if (clearDataButton) {
            clearDataButton.addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all growth tracking data? This cannot be undone.')) {
                    localStorage.removeItem('growthTrackerData');

                    // Clear the table
                    const tableBody = document.getElementById('growthTableBody');
                    if (tableBody) {
                        tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center;">No data available yet</td></tr>';
                    }

                    // Clear the chart
                    const chartContainer = document.getElementById('growthChart');
                    if (chartContainer) {
                        chartContainer.innerHTML = '<p>Chart visualization will appear here after adding measurements</p>';
                    }

                    alert('All growth tracking data has been cleared.');
                }
            });
        }

        // Add event listeners for chart controls
        const chartTypeSelect = document.getElementById('chartType');
        const chartPeriodSelect = document.getElementById('chartPeriod');

        if (chartTypeSelect) {
            chartTypeSelect.addEventListener('change', function() {
                const savedData = localStorage.getItem('growthTrackerData');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    displayGrowthData(data);
                }
            });
        }

        if (chartPeriodSelect) {
            chartPeriodSelect.addEventListener('change', function() {
                const savedData = localStorage.getItem('growthTrackerData');
                if (savedData) {
                    let data = JSON.parse(savedData);

                    // Filter data based on selected period
                    if (this.value !== 'all') {
                        const now = new Date();
                        const monthsAgo = this.value === '3months' ? 3 : 6;
                        const cutoffDate = new Date(now.setMonth(now.getMonth() - monthsAgo));

                        data = data.filter(entry => {
                            const entryDate = new Date(entry.date);
                            return !isNaN(entryDate.getTime()) && entryDate >= cutoffDate;
                        });
                    }

                    displayGrowthData(data);
                }
            });
        }

        // Function to generate sample data for demonstration
        const generateSampleData = () => {
            // Check if we already have data
            const savedData = localStorage.getItem('growthTrackerData');
            if (savedData && JSON.parse(savedData).length > 0) {
                return; // Don't generate sample data if we already have data
            }

            // Create sample data for a baby from birth to 12 months
            const sampleData = [];
            const today = new Date();

            // Generate data points for each month
            for (let month = 0; month <= 12; month++) {
                const measurementDate = new Date(today);
                measurementDate.setMonth(today.getMonth() - (12 - month));

                // Format the date as YYYY-MM-DD for the input field
                const formattedDate = measurementDate.toISOString().split('T')[0];

                // Create realistic growth data based on WHO growth standards
                // These are approximate values for demonstration purposes
                const weight = month === 0 ? 3.5 : 3.5 + (month * 0.5 + Math.random() * 0.3);
                const height = month === 0 ? 50 : 50 + (month * 2 + Math.random() * 0.5);
                const headCircumference = month === 0 ? 35 : 35 + (month * 0.5 + Math.random() * 0.2);

                sampleData.push({
                    date: formattedDate,
                    age: month === 0 ? 'Birth' : `${month} month${month > 1 ? 's' : ''}`,
                    weight: weight.toFixed(2),
                    weightUnit: 'kg',
                    height: height.toFixed(1),
                    heightUnit: 'cm',
                    headCircumference: headCircumference.toFixed(1)
                });
            }

            // Save sample data to localStorage
            localStorage.setItem('growthTrackerData', JSON.stringify(sampleData));

            // Display the sample data
            displayGrowthData(sampleData);

            console.log('Sample growth data generated for demonstration');
        };

        // Add a button to generate sample data
        const addSampleDataButton = document.createElement('button');
        addSampleDataButton.textContent = 'Generate Sample Data';
        addSampleDataButton.className = 'btn btn-secondary';
        addSampleDataButton.style.marginRight = 'auto'; // Push to the left
        addSampleDataButton.addEventListener('click', function() {
            generateSampleData();
        });

        // Add the button to the form actions
        const formActions = document.querySelector('#growthTrackerForm .form-actions');
        formActions.insertBefore(addSampleDataButton, formActions.firstChild);

        // Load data when page loads
        loadSavedData();

        // Generate sample data if no data exists
        const savedData = localStorage.getItem('growthTrackerData');
        if (!savedData || JSON.parse(savedData).length === 0) {
            // Automatically generate sample data for demonstration
            generateSampleData();
        }
    }

    // Vaccination Schedule
    const vaccinationSchedule = document.getElementById('vaccinationSchedule');
    if (vaccinationSchedule) {
        const toggleVaccineDetails = (e) => {
            if (e.target.classList.contains('vaccine-toggle') || e.target.parentElement.classList.contains('vaccine-toggle')) {
                const button = e.target.classList.contains('vaccine-toggle') ? e.target : e.target.parentElement;
                const detailsContainer = button.nextElementSibling;

                // Toggle visibility
                if (detailsContainer.style.maxHeight) {
                    detailsContainer.style.maxHeight = null;
                    button.querySelector('i').className = 'fas fa-chevron-down';
                } else {
                    detailsContainer.style.maxHeight = detailsContainer.scrollHeight + 'px';
                    button.querySelector('i').className = 'fas fa-chevron-up';
                }
            }
        };

        // Add click event listener to the schedule container
        vaccinationSchedule.addEventListener('click', toggleVaccineDetails);

        // Set reminder functionality
        const reminderForms = document.querySelectorAll('.reminder-form');
        reminderForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const vaccineName = this.getAttribute('data-vaccine');
                const reminderDate = this.querySelector('input[type="date"]').value;

                if (!reminderDate) {
                    alert('Please select a date for the reminder');
                    return;
                }

                // In a real app, this would connect to a backend to set reminders
                // For now, we'll just store in localStorage
                let reminders = {};
                const savedReminders = localStorage.getItem('vaccineReminders');
                if (savedReminders) {
                    reminders = JSON.parse(savedReminders);
                }

                reminders[vaccineName] = reminderDate;
                localStorage.setItem('vaccineReminders', JSON.stringify(reminders));

                alert(`Reminder set for ${vaccineName} on ${new Date(reminderDate).toLocaleDateString()}`);

                // Update UI
                const reminderStatus = this.nextElementSibling;
                reminderStatus.textContent = `Reminder set for: ${new Date(reminderDate).toLocaleDateString()}`;
                reminderStatus.style.display = 'block';
            });
        });

        // Load existing reminders
        const loadReminders = () => {
            const savedReminders = localStorage.getItem('vaccineReminders');
            if (savedReminders) {
                const reminders = JSON.parse(savedReminders);

                for (const vaccine in reminders) {
                    const form = document.querySelector(`.reminder-form[data-vaccine="${vaccine}"]`);
                    if (form) {
                        const reminderStatus = form.nextElementSibling;
                        reminderStatus.textContent = `Reminder set for: ${new Date(reminders[vaccine]).toLocaleDateString()}`;
                        reminderStatus.style.display = 'block';
                    }
                }
            }
        };

        loadReminders();
    }
});
