const Activity = require('../models/Activity');

// Get all activities with filtering, sorting, and pagination (admin only)
exports.getAllActivities = async (req, res, next) => {
  try {
    // Implement pagination, filtering, and sorting
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 20;
    const skip = (page - 1) * limit;

    // Build query
    let query = Activity.find();

    // Filter by user
    if (req.query.user) {
      query = query.find({ user: req.query.user });
    }

    // Filter by action
    if (req.query.action) {
      query = query.find({ action: req.query.action });
    }

    // Filter by date range
    if (req.query.startDate && req.query.endDate) {
      query = query.find({
        timestamp: {
          $gte: new Date(req.query.startDate),
          $lte: new Date(req.query.endDate),
        },
      });
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-timestamp');
    }

    // Populate user
    query = query.populate({
      path: 'user',
      select: 'firstName lastName email',
    });

    // Execute query with pagination
    const activities = await query.skip(skip).limit(limit);

    // Get total count for pagination
    const total = await Activity.countDocuments(query.getFilter());

    res.status(200).json({
      success: true,
      count: activities.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
      data: activities,
    });
  } catch (error) {
    next(error);
  }
};

// Get activity statistics (admin only)
exports.getActivityStats = async (req, res, next) => {
  try {
    // Get activity counts by type for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const stats = await Activity.aggregate([
      {
        $match: {
          timestamp: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Get daily activity counts for the last 30 days
    const dailyStats = await Activity.aggregate([
      {
        $match: {
          timestamp: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        actionStats: stats,
        dailyStats,
      },
    });
  } catch (error) {
    next(error);
  }
};
