const User = require('../models/User');
const fs = require('fs');

// Function to check if MongoDB is available
const isMongoDBAvailable = () => {
  try {
    // Check mongoose connection state
    // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting
    return User.db.readyState === 1;
  } catch (error) {
    fs.appendFileSync('server-log.txt', `User controller error checking MongoDB: ${error.message}\n`);
    return false;
  }
};

// Log MongoDB connection status on startup
try {
  if (!isMongoDBAvailable()) {
    fs.appendFileSync('server-log.txt', 'User controller: MongoDB is not connected. Using fallback mode.\n');
  }
} catch (error) {
  fs.appendFileSync('server-log.txt', `User controller error: ${error.message}\n`);
}

// @desc    Update user profile
// @route   PUT /api/profile
// @access  Private
exports.updateProfile = async (req, res) => {
  try {
    // Log update profile attempt
    fs.appendFileSync('server-log.txt', `Update profile attempt for user: ${req.user.id}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for update profile\n');

      fs.appendFileSync('server-log.txt', 'Mock profile update successful\n');

      return res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        data: {
          ...req.user,
          ...req.body
        }
      });
    }

    // Fields to update
    const fieldsToUpdate = {};

    // Allowed fields to update
    const allowedFields = [
      'firstName',
      'lastName',
      'phoneNumber',
      'address',
      'dateOfBirth',
      'pregnancyDueDate',
      'childrenInfo'
    ];

    // Add fields to update object
    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        fieldsToUpdate[field] = req.body[field];
      }
    }

    // Update user
    const user = await User.findByIdAndUpdate(
      req.user.id,
      fieldsToUpdate,
      {
        new: true,
        runValidators: true
      }
    );

    if (!user) {
      fs.appendFileSync('server-log.txt', `Update profile failed: User with id ${req.user.id} not found\n`);
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    fs.appendFileSync('server-log.txt', `Update profile successful for user: ${req.user.id}\n`);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update profile error:', error);
    fs.appendFileSync('server-log.txt', `Update profile error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during profile update'
    });
  }
};

// @desc    Update password
// @route   PUT /api/password
// @access  Private
exports.updatePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validate passwords
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }

    // Log update password attempt
    fs.appendFileSync('server-log.txt', `Update password attempt for user: ${req.user.id}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for update password\n');

      fs.appendFileSync('server-log.txt', 'Mock password update successful\n');

      return res.status(200).json({
        success: true,
        message: 'Password updated successfully'
      });
    }

    // Get user with password
    const user = await User.findById(req.user.id).select('+password');

    if (!user) {
      fs.appendFileSync('server-log.txt', `Update password failed: User with id ${req.user.id} not found\n`);
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check current password
    const isMatch = await user.matchPassword(currentPassword);

    if (!isMatch) {
      fs.appendFileSync('server-log.txt', `Update password failed: Current password is incorrect for user ${req.user.id}\n`);
      return res.status(401).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    fs.appendFileSync('server-log.txt', `Update password successful for user: ${req.user.id}\n`);

    res.status(200).json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('Update password error:', error);
    fs.appendFileSync('server-log.txt', `Update password error: ${error.message}\n`);
    res.status(500).json({
      success: false,
      message: 'Server error during password update'
    });
  }
};
