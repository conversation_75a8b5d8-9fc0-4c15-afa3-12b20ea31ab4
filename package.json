{"name": "maternify", "version": "1.0.0", "description": "Pregnancy and Infant Care Website - Runs on port 8080", "main": "server.js", "scripts": {"start": "node server/server.js", "server": "nodemon server/server.js", "seed": "node server/config/seedData.js", "install-server": "cd server && npm install", "dev": "npm run server"}, "keywords": ["pregnancy", "infant", "care", "parenting"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.3"}, "devDependencies": {"nodemon": "^3.0.1"}}