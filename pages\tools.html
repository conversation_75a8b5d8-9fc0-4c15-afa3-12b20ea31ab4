<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parenting Tools | MATERNIFY</title>
    <meta name="description" content="Interactive tools for pregnancy and infant care including due date calculator, BMI tracker, growth tracking, and vaccination schedule.">
    <link rel="stylesheet" href="../css/styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        /* Page-specific styles */
        .tools-nav {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-xl);
        }

        .tool-nav-item {
            padding: var(--spacing-md) var(--spacing-lg);
            background-color: var(--light-bg);
            border-radius: var(--border-radius-md);
            font-family: var(--heading-font);
            font-weight: 500;
            color: var(--text-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }

        .tool-nav-item i {
            margin-right: var(--spacing-sm);
            font-size: 1.2rem;
        }

        .tool-nav-item:hover {
            background-color: var(--accent-color);
            transform: translateY(-3px);
        }

        .tool-section {
            padding: var(--spacing-xl) 0;
            border-top: 1px solid var(--border-color);
            scroll-margin-top: 100px;
        }

        .tool-section:first-of-type {
            border-top: none;
        }

        .tool-header {
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
        }

        .tool-header i {
            font-size: 2rem;
            color: var(--primary-dark);
            margin-right: var(--spacing-md);
        }

        .tool-header h2 {
            margin-bottom: 0;
        }

        .tool-description {
            margin-bottom: var(--spacing-lg);
            max-width: 800px;
        }

        .tool-container {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-actions {
            margin-top: var(--spacing-lg);
            display: flex;
            justify-content: flex-end;
        }

        .result-card {
            background-color: var(--light-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }

        .result-card h3 {
            color: var(--primary-dark);
            margin-bottom: var(--spacing-md);
            text-align: center;
        }

        .due-date, .bmi-value {
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: var(--spacing-md);
            color: var(--primary-dark);
        }

        .pregnancy-week, .bmi-category {
            text-align: center;
            margin-bottom: var(--spacing-md);
            font-weight: 500;
        }

        .trimester-info, .bmi-advice {
            background-color: var(--white);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
        }

        .bmi-category.underweight { color: #3498db; }
        .bmi-category.normal { color: #2ecc71; }
        .bmi-category.overweight { color: #f39c12; }
        .bmi-category.obese { color: #e74c3c; }

        .disclaimer {
            font-size: 0.875rem;
            font-style: italic;
            margin-top: var(--spacing-md);
            color: var(--text-light);
        }

        .growth-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: var(--spacing-lg);
        }

        .growth-table th, .growth-table td {
            padding: var(--spacing-sm);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .growth-table th {
            background-color: var(--light-bg);
            font-weight: 600;
        }

        .growth-chart {
            background-color: var(--light-bg);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-md);
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--spacing-lg);
        }

        .vaccine-age-group {
            margin-bottom: var(--spacing-xl);
        }

        .vaccine-age-header {
            background-color: var(--secondary-color);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
            font-weight: 600;
        }

        .vaccine-list {
            background-color: var(--white);
            border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
            box-shadow: var(--shadow-sm);
        }

        .vaccine-item {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }

        .vaccine-item:last-child {
            border-bottom: none;
        }

        .vaccine-name {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .vaccine-toggle {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            padding: var(--spacing-xs);
        }

        .vaccine-details {
            padding-top: var(--spacing-sm);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .vaccine-description {
            margin-bottom: var(--spacing-sm);
            font-size: 0.95rem;
        }

        .reminder-form {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-sm);
        }

        .reminder-status {
            font-size: 0.875rem;
            color: var(--primary-dark);
            margin-top: var(--spacing-xs);
            display: none;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                    <li><a href="infant-care.html">Infant Care</a></li>
                    <li><a href="tools.html" class="active">Tools</a></li>
                    <li><a href="community.html">Community</a></li>
                    <li><a href="blog.html">Articles</a></li>
                    <li><a href="login.html" class="btn-login">Login</a></li>
                    <li><a href="register.html" class="btn-register">Register</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Parenting Tools</h1>
            <p>Interactive tools to support you through pregnancy and infant care.</p>
        </div>
    </section>

    <section class="tools-overview">
        <div class="container">
            <div class="tools-nav">
                <a href="#due-date" class="tool-nav-item">
                    <i class="far fa-calendar-alt"></i> Due Date Calculator
                </a>
                <a href="#bmi" class="tool-nav-item">
                    <i class="fas fa-weight"></i> BMI Tracker
                </a>
                <a href="#growth-tracker" class="tool-nav-item">
                    <i class="fas fa-chart-line"></i> Growth Tracker
                </a>
                <a href="#vaccination" class="tool-nav-item">
                    <i class="fas fa-syringe"></i> Vaccination Schedule
                </a>
            </div>

            <!-- Due Date Calculator -->
            <div id="due-date" class="tool-section">
                <div class="tool-header">
                    <i class="far fa-calendar-alt"></i>
                    <h2>Due Date Calculator</h2>
                </div>
                <p class="tool-description">
                    Estimate your baby's due date based on your last menstrual period (LMP). This calculator uses the standard method of adding 280 days (40 weeks) to the first day of your last period.
                </p>
                <div class="tool-container">
                    <form id="dueDateCalculator">
                        <div class="form-group">
                            <label for="lmpDate">First day of your last menstrual period</label>
                            <input type="date" id="lmpDate" name="lmpDate" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Calculate Due Date</button>
                        </div>
                    </form>
                    <div id="dueDateResult"></div>
                </div>
                <p class="disclaimer">
                    Note: This calculator provides an estimate based on a standard 28-day cycle. For a more accurate due date, consult with your healthcare provider, especially if you have irregular cycles.
                </p>
            </div>

            <!-- BMI Tracker -->
            <div id="bmi" class="tool-section">
                <div class="tool-header">
                    <i class="fas fa-weight"></i>
                    <h2>BMI Tracker</h2>
                </div>
                <p class="tool-description">
                    Calculate your Body Mass Index (BMI) to monitor your weight during pregnancy. BMI is a measure of body fat based on height and weight that applies to adult women and men.
                </p>
                <div class="tool-container">
                    <form id="bmiCalculator">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="weight">Weight (kg)</label>
                                <input type="number" id="weight" name="weight" min="30" max="300" step="0.1" required>
                            </div>
                            <div class="form-group">
                                <label for="height">Height (cm)</label>
                                <input type="number" id="height" name="height" min="100" max="250" step="0.1" required>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Calculate BMI</button>
                        </div>
                    </form>
                    <div id="bmiResult"></div>
                </div>
                <p class="disclaimer">
                    Note: BMI is just one measure of health and may not be accurate for all body types. During pregnancy, weight gain recommendations vary based on pre-pregnancy BMI. Always consult with your healthcare provider for personalized advice.
                </p>
            </div>

            <!-- Growth Tracker -->
            <div id="growth-tracker" class="tool-section">
                <div class="tool-header">
                    <i class="fas fa-chart-line"></i>
                    <h2>Growth Tracker</h2>
                </div>
                <p class="tool-description">
                    Monitor your baby's growth by recording weight, height, and head circumference measurements over time. This tool helps you visualize your baby's development and share the information with your pediatrician.
                </p>
                <div class="tool-container">
                    <form id="growthTrackerForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="measurementDate">Date of Measurement</label>
                                <input type="date" id="measurementDate" name="measurementDate" required>
                            </div>
                            <div class="form-group">
                                <label for="babyAge">Baby's Age</label>
                                <input type="text" id="babyAge" name="babyAge" placeholder="e.g., 2 months" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="babyWeight">Weight</label>
                                <input type="number" id="babyWeight" name="babyWeight" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="weightUnit">Unit</label>
                                <select id="weightUnit" name="weightUnit">
                                    <option value="kg">Kilograms (kg)</option>
                                    <option value="lb">Pounds (lb)</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="babyHeight">Length/Height</label>
                                <input type="number" id="babyHeight" name="babyHeight" step="0.1" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="heightUnit">Unit</label>
                                <select id="heightUnit" name="heightUnit">
                                    <option value="cm">Centimeters (cm)</option>
                                    <option value="in">Inches (in)</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="headCircumference">Head Circumference (cm)</label>
                                <input type="number" id="headCircumference" name="headCircumference" step="0.1" min="0">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Measurement</button>
                        </div>
                    </form>

                    <div class="growth-data" style="margin-top: var(--spacing-xl);">
                        <h3>Growth History</h3>
                        <div id="growthChart" class="growth-chart">
                            <p>Chart visualization will appear here after adding measurements</p>
                        </div>
                        <div class="growth-table-container">
                            <table class="growth-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Age</th>
                                        <th>Weight</th>
                                        <th>Height</th>
                                        <th>Head Circ.</th>
                                    </tr>
                                </thead>
                                <tbody id="growthTableBody">
                                    <tr>
                                        <td colspan="5" style="text-align: center;">No data available yet</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <p class="disclaimer">
                    Note: This tool is for personal tracking only and does not replace professional medical advice. Always consult with your pediatrician about your baby's growth and development.
                </p>
            </div>

            <!-- Vaccination Schedule -->
            <div id="vaccination" class="tool-section">
                <div class="tool-header">
                    <i class="fas fa-syringe"></i>
                    <h2>Vaccination Schedule</h2>
                </div>
                <p class="tool-description">
                    Keep track of recommended vaccines for your child based on age. This schedule follows standard guidelines, but your healthcare provider may recommend a different schedule based on your child's specific needs.
                </p>
                <div class="tool-container">
                    <div id="vaccinationSchedule">
                        <!-- Birth to 2 Months -->
                        <div class="vaccine-age-group">
                            <div class="vaccine-age-header">
                                Birth to 2 Months
                            </div>
                            <div class="vaccine-list">
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        Hepatitis B (HepB)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose recommended at birth, second dose at 1-2 months. Protects against hepatitis B virus infection, which can lead to liver disease.
                                        </p>
                                        <form class="reminder-form" data-vaccine="HepB">
                                            <label for="hepbReminder">Set reminder:</label>
                                            <input type="date" id="hepbReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 2 Months -->
                        <div class="vaccine-age-group">
                            <div class="vaccine-age-header">
                                2 Months
                            </div>
                            <div class="vaccine-list">
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        DTaP (Diphtheria, Tetanus, Pertussis)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against diphtheria, tetanus (lockjaw), and pertussis (whooping cough).
                                        </p>
                                        <form class="reminder-form" data-vaccine="DTaP-1">
                                            <label for="dtapReminder">Set reminder:</label>
                                            <input type="date" id="dtapReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        IPV (Polio)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against poliomyelitis, which can cause permanent paralysis.
                                        </p>
                                        <form class="reminder-form" data-vaccine="IPV-1">
                                            <label for="ipvReminder">Set reminder:</label>
                                            <input type="date" id="ipvReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        Hib (Haemophilus influenzae type b)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against Haemophilus influenzae type b, which can cause meningitis and other serious infections.
                                        </p>
                                        <form class="reminder-form" data-vaccine="Hib-1">
                                            <label for="hibReminder">Set reminder:</label>
                                            <input type="date" id="hibReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        PCV13 (Pneumococcal conjugate)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against pneumococcal disease, which can cause ear infections, pneumonia, and meningitis.
                                        </p>
                                        <form class="reminder-form" data-vaccine="PCV13-1">
                                            <label for="pcvReminder">Set reminder:</label>
                                            <input type="date" id="pcvReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        RV (Rotavirus)
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            First dose at 2 months. Protects against rotavirus, which causes severe diarrhea and vomiting.
                                        </p>
                                        <form class="reminder-form" data-vaccine="RV-1">
                                            <label for="rvReminder">Set reminder:</label>
                                            <input type="date" id="rvReminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 4 Months -->
                        <div class="vaccine-age-group">
                            <div class="vaccine-age-header">
                                4 Months
                            </div>
                            <div class="vaccine-list">
                                <div class="vaccine-item">
                                    <div class="vaccine-name">
                                        DTaP (Diphtheria, Tetanus, Pertussis) - 2nd dose
                                        <button class="vaccine-toggle" aria-label="Toggle vaccine details">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <div class="vaccine-details">
                                        <p class="vaccine-description">
                                            Second dose at 4 months. Continues to build protection against diphtheria, tetanus, and pertussis.
                                        </p>
                                        <form class="reminder-form" data-vaccine="DTaP-2">
                                            <label for="dtap2Reminder">Set reminder:</label>
                                            <input type="date" id="dtap2Reminder" name="reminderDate">
                                            <button type="submit" class="btn btn-sm">Set</button>
                                        </form>
                                        <div class="reminder-status"></div>
                                    </div>
                                </div>
                                <!-- Additional 4-month vaccines would be listed here -->
                            </div>
                        </div>

                        <!-- Additional age groups would be added here -->
                    </div>
                </div>
                <p class="disclaimer">
                    Note: This vaccination schedule is based on recommendations from major health organizations. Always consult with your pediatrician for the most appropriate vaccination schedule for your child.
                </p>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="infant-care.html">Infant Care</a></li>
                        <li><a href="tools.html">Tools</a></li>
                        <li><a href="community.html">Community</a></li>
                        <li><a href="blog.html">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="#due-date">Due Date Calculator</a></li>
                        <li><a href="#bmi">BMI Tracker</a></li>
                        <li><a href="#growth-tracker">Growth Tracker</a></li>
                        <li><a href="#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
</body>
</html>
