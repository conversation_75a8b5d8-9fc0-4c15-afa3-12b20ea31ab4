# MATERNIFY - Pregnancy & Infant Care Platform

A comprehensive platform for pregnancy and infant care with tools, guidelines, community support, and admin dashboard.

## Technologies Used

- **Frontend**: HTML, CSS, JavaScript
- **Backend**: Node.js, Express.js
- **Database**: MongoDB
- **Authentication**: JWT (JSON Web Tokens)
- **Styling**: Custom CSS with responsive design

## Project Structure

```
pregnancy-infant-care/
├── public/                # Frontend static files
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── images/            # Image assets
│   └── pages/             # HTML pages
├── server/                # Backend code
│   ├── config/            # Configuration files
│   ├── controllers/       # Route controllers
│   ├── middleware/        # Express middleware
│   ├── models/            # Mongoose models
│   └── routes/            # API routes
├── .env                   # Environment variables
├── package.json           # Project dependencies
└── README.md              # Project documentation
```

## Features

- **Responsive Design**: Optimized for all devices
- **User Authentication**: Secure login and registration system
- **Admin Dashboard**: Comprehensive admin panel for managing users, content, and system settings
- **Pregnancy Guide**: Expert guidance for pregnancy care by trimester
- **Infant Care**: Resources for infant care and development milestones
- **Interactive Tools**:
  - Due date calculator
  - BMI tracker
  - Growth tracker with chart visualization
  - Vaccination schedule
- **Community Forum**: Connect with other parents
- **Articles & Resources**: Informative content on pregnancy and infant care
- **Video Resources**: Educational videos on pregnancy, childbirth, and infant care

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)
- MongoDB (local or Atlas)

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd pregnancy-infant-care
   ```

2. Install server dependencies:
   ```
   npm run install-server
   ```

3. Set up environment variables:
   - Create a `.env` file in the server directory if it doesn't exist
   - Add the following variables:
     ```
     PORT=8080
     MONGODB_URI=mongodb://localhost:27017/maternify
     JWT_SECRET=your_jwt_secret_key_here
     JWT_EXPIRES_IN=30d
     NODE_ENV=development
     ```

4. Seed the database with initial data:
   ```
   npm run seed
   ```

5. Start the server:
   ```
   npm run dev
   ```

6. Access the application:
   - Open your browser and go to: http://localhost:8080
   - For the admin dashboard: http://localhost:8080/admin-dashboard

### Admin Access

Use the following credentials to access the admin dashboard:
- Email: <EMAIL>
- Password: admin123

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout user

### Users
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create a new user (admin only)
- `PATCH /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (admin only)

### Content
- `GET /api/content` - Get all content
- `GET /api/content/:id` - Get content by ID
- `POST /api/content` - Create new content (admin only)
- `PATCH /api/content/:id` - Update content (admin only)
- `DELETE /api/content/:id` - Delete content (admin only)

### Dashboard
- `GET /api/dashboard/stats` - Get admin dashboard statistics (admin only)

### Activities
- `GET /api/activities` - Get all activities (admin only)
- `GET /api/activities/stats` - Get activity statistics (admin only)

### System Logs
- `GET /api/logs` - Get all system logs (admin only)
- `GET /api/logs/stats` - Get log statistics (admin only)

### Settings
- `GET /api/settings` - Get all settings (admin only)
- `GET /api/settings/:key` - Get setting by key
- `PATCH /api/settings/:key` - Update setting (admin only)
- `PATCH /api/settings` - Update multiple settings (admin only)

## Future Enhancements

- Real-time notifications
- Enhanced analytics dashboard
- Mobile app version
- Multilingual support
- Integration with health tracking devices
- AI-powered content recommendations

## License

This project is licensed under the MIT License - see the LICENSE file for details.
