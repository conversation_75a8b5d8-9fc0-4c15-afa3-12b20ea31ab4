<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MATERNIFY - Pregnancy & Infant Care</title>
    <meta name="description" content="Comprehensive resource for pregnancy and infant care with tools, guidelines, and community support.">
    <link rel="stylesheet" href="/css/styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Custom styles for header to match login page -->
    <style>
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .nav-menu li a.btn-login,
        header .nav-menu li a.btn-register {
            color: var(--text-color) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }
    </style>
    <style>
        /* Custom styles with more saturated colors */
        :root {
            /* More saturated color palette */
            --primary-color: #ff9fb6; /* Saturated Pink */
            --primary-dark: #ff6b8b;
            --secondary-color: #7ac5ff; /* Saturated Blue */
            --secondary-dark: #4a9eff;
            --accent-color: #ffda6a; /* Saturated Cream/Yellow */
            --accent-dark: #ffc234;
            --text-color: #4a4a4a;
            --text-light: #555555;
        }

        /* Removed welcome-hero styles as it's no longer needed */

        /* Footer styling to match other pages */
        footer {
            background-color: var(--text-color);
            color: var(--white);
            padding: var(--spacing-xxl) 0 var(--spacing-lg);
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }

        .footer-about h3 {
            color: var(--white);
            margin-bottom: var(--spacing-md);
        }

        .social-links {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: var(--white);
            transition: background-color 0.3s ease;
        }

        .social-links a:hover {
            background-color: var(--primary-color);
            color: var(--text-color);
        }

        .footer-links h4,
        .footer-tools h4,
        .footer-newsletter h4 {
            color: var(--white);
            margin-bottom: var(--spacing-md);
        }

        .footer-links ul li,
        .footer-tools ul li {
            margin-bottom: var(--spacing-sm);
        }

        .footer-links a,
        .footer-tools a {
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s ease;
        }

        .footer-links a:hover,
        .footer-tools a:hover {
            color: var(--primary-color);
        }

        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--spacing-lg);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .footer-bottom p {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        .footer-bottom-links {
            display: flex;
            gap: var(--spacing-lg);
        }

        .footer-bottom-links a {
            color: rgba(255, 255, 255, 0.5);
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .footer-bottom-links a:hover {
            color: var(--primary-color);
        }

        /* Simple footer social links (for compatibility) */
        .simple-social-links {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
            justify-content: center;
            margin-bottom: var(--spacing-lg);
        }

        .simple-social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: var(--white);
            transition: background-color 0.3s ease;
        }

        .simple-social-links a:hover {
            background-color: var(--primary-color);
            color: var(--text-color);
        }

        .simple-footer-links {
            display: flex;
            justify-content: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            flex-wrap: wrap;
        }

        .simple-footer-links a {
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s ease;
        }

        .simple-footer-links a:hover {
            color: var(--primary-color);
        }

        /* Header styling is now handled by the custom styles at the top */

        /* Responsive adjustments */
        @media (max-width: 768px) {
            /* Header adjustments */
            header .container {
                padding: 0 var(--spacing-md);
            }

            .logo h1 {
                font-size: 1.3rem;
            }

            /* Mobile nav styling is now handled by the custom styles at the top */

            .nav-menu li {
                margin: var(--spacing-sm) 0;
            }

            .btn-login, .btn-register {
                width: 100%;
                text-align: center;
                margin: var(--spacing-xs) 0;
            }

            /* Content adjustments */
            .about-content {
                padding: var(--spacing-lg);
                margin: 0 var(--spacing-md);
            }

            .about-title {
                font-size: 2rem;
            }

            .about-text p {
                font-size: 1rem;
            }

            /* Footer adjustments */
            .simple-footer-links {
                gap: var(--spacing-md);
            }

            .simple-footer-links a {
                padding: 5px;
                font-size: 0.9rem;
            }
        }

        /* Animation for buttons - applied to about-btn */
        .about-btn {
            position: relative;
            overflow: hidden;
        }

        .about-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .about-btn:hover::after {
            transform: translateX(100%);
        }

        /* Main content section styles (formerly about section) */
        .about-section {
            background: linear-gradient(135deg, rgba(255, 159, 182, 0.1) 0%, rgba(122, 197, 255, 0.1) 100%), url('images/pexels-kpaukshtite-3242264.jpg');
            background-size: cover;
            background-position: center;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .about-content {
            max-width: 1000px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.95);
            padding: var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .about-title {
            font-size: 2.5rem;
            color: var(--primary-dark);
            text-align: center;
            margin-bottom: var(--spacing-xl);
            position: relative;
            padding-bottom: var(--spacing-md);
        }

        .about-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: var(--primary-dark);
            border-radius: 2px;
        }

        .about-grid {
            display: flex;
            gap: var(--spacing-xl);
            align-items: center;
        }

        .about-text {
            flex: 1;
        }

        .about-text p {
            margin-bottom: var(--spacing-md);
            font-size: 1.05rem;
            line-height: 1.7;
            color: var(--text-color);
        }

        .about-features {
            list-style: none;
            margin: var(--spacing-lg) 0;
        }

        .about-features li {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-left: var(--spacing-md);
        }

        .about-features i {
            color: var(--primary-dark);
            font-size: 1.2rem;
            margin-right: var(--spacing-md);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .about-features span {
            font-size: 1.05rem;
        }

        .about-cta {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .about-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .about-btn i {
            margin-right: 8px;
        }

        .login-btn {
            background-color: transparent;
            color: var(--primary-dark);
            border: 2px solid var(--primary-dark);
        }

        .login-btn:hover {
            background-color: var(--primary-dark);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .register-btn {
            background-color: var(--primary-dark);
            color: var(--white);
            border: 2px solid var(--primary-dark);
        }

        .register-btn:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 4px 10px rgba(255, 107, 139, 0.3);
        }

        @media (max-width: 768px) {
            .about-section {
                padding: 0;
            }

            .about-title {
                font-size: 1.8rem;
            }

            .about-grid {
                flex-direction: column;
                gap: var(--spacing-lg);
            }

            .about-text p,
            .about-features span {
                font-size: 1rem;
            }

            .about-cta {
                flex-direction: column;
                width: 100%;
            }

            .about-btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/" class="active">Home</a></li>
                    <li><a href="/login" class="btn-login">Login</a></li>
                    <li><a href="/register" class="btn-register">Register</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="about-section" style="margin-top: 0; padding-top: 0;">
        <div class="container">
            <div class="about-content">
                <h2 class="about-title">Welcome to MATERNIFY</h2>
                <div class="about-grid">
                    <div class="about-text">
                        <p>MATERNIFY is a comprehensive platform dedicated to supporting parents through every stage of their pregnancy and infant care journey. Our mission is to provide reliable information, practical tools, and a supportive community for new and expecting parents.</p>

                        <p>With MATERNIFY, you can:</p>

                        <ul class="about-features">
                            <li>
                                <i class="fas fa-calendar-check"></i>
                                <span>Track your pregnancy milestones and due date</span>
                            </li>
                            <li>
                                <i class="fas fa-chart-line"></i>
                                <span>Monitor your baby's growth with visual charts</span>
                            </li>
                            <li>
                                <i class="fas fa-book-medical"></i>
                                <span>Access expert-reviewed pregnancy and infant care guides</span>
                            </li>
                            <li>
                                <i class="fas fa-syringe"></i>
                                <span>Keep track of vaccination schedules and health records</span>
                            </li>
                            <li>
                                <i class="fas fa-users"></i>
                                <span>Connect with a community of parents sharing similar experiences</span>
                            </li>
                        </ul>

                        <p>Whether you're a first-time parent or growing your family, MATERNIFY provides the tools and resources you need for a healthy pregnancy and confident parenting journey.</p>

                        <div class="about-cta">
                            <a href="/login" class="about-btn login-btn">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                            <a href="/register" class="about-btn register-btn">
                                <i class="fas fa-user-plus"></i> Register
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/login">Login</a></li>
                        <li><a href="/register">Register</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="javascript:void(0);" onclick="alert('Please log in to access this tool');">Due Date Calculator</a></li>
                        <li><a href="javascript:void(0);" onclick="alert('Please log in to access this tool');">BMI Tracker</a></li>
                        <li><a href="javascript:void(0);" onclick="alert('Please log in to access this tool');">Growth Tracker</a></li>
                        <li><a href="javascript:void(0);" onclick="alert('Please log in to access this tool');">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>
</body>
</html>
