<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregnancy Care Guidelines | MATERNIFY</title>
    <meta name="description" content="Comprehensive trimester-wise pregnancy care guidelines including diet plans, exercise suggestions, and symptom management tips.">
    <link rel="stylesheet" href="../css/styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        /* Custom styles for header to match footer */
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        .user-menu {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        .user-greeting {
            color: var(--white) !important;
        }
        .logout-btn {
            color: var(--white) !important;
        }
        .logout-btn:hover {
            color: var(--primary-color) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }

        /* Page-specific styles */
        .trimester-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: var(--spacing-xl);
            border-bottom: 1px solid var(--border-color);
        }

        .tab-button {
            padding: var(--spacing-md) var(--spacing-lg);
            background: none;
            border: none;
            font-family: var(--heading-font);
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--text-light);
            cursor: pointer;
            position: relative;
            transition: color 0.3s ease;
        }

        .tab-button::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 0;
            height: 3px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }

        .tab-button.active {
            color: var(--primary-dark);
        }

        .tab-button.active::after {
            width: 100%;
        }

        .tab-button:hover {
            color: var(--primary-dark);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .care-category {
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            background-color: var(--light-bg);
        }

        .care-category h3 {
            color: var(--primary-dark);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
        }

        .care-category h3 i {
            margin-right: var(--spacing-sm);
        }

        .care-list {
            list-style-type: none;
        }

        .care-list li {
            margin-bottom: var(--spacing-md);
            padding-left: var(--spacing-lg);
            position: relative;
        }

        .care-list li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 10px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--primary-color);
        }

        .care-tip {
            background-color: var(--accent-color);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-top: var(--spacing-md);
        }

        .care-tip h4 {
            margin-bottom: var(--spacing-sm);
            font-size: 1.1rem;
        }


    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li>
                        <div class="user-menu">
                            <span class="user-greeting">Hello, <span id="nav-user-name">User</span>!</span>
                            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Pregnancy Care Guidelines</h1>
            <p>Comprehensive trimester-wise advice to support you through your pregnancy journey.</p>
        </div>
    </section>

    <section class="pregnancy-care">
        <div class="container">
            <div class="trimester-tabs">
                <button class="tab-button active" data-tab="first-trimester">First Trimester</button>
                <button class="tab-button" data-tab="second-trimester">Second Trimester</button>
                <button class="tab-button" data-tab="third-trimester">Third Trimester</button>
            </div>

            <div id="first-trimester" class="tab-content active">
                <div class="care-category">
                    <h3><i class="fas fa-utensils"></i> Nutrition Guidelines</h3>
                    <ul class="care-list">
                        <li><strong>Folic Acid:</strong> Consume 400-800 micrograms daily to prevent neural tube defects. Sources include leafy greens, fortified cereals, and supplements.</li>
                        <li><strong>Iron-Rich Foods:</strong> Include lean meats, beans, spinach, and fortified cereals to support increased blood volume.</li>
                        <li><strong>Calcium Sources:</strong> Dairy products, fortified plant milks, and leafy greens to support bone development.</li>
                        <li><strong>Hydration:</strong> Aim for 8-10 glasses of water daily to support amniotic fluid and prevent constipation.</li>
                        <li><strong>Small, Frequent Meals:</strong> Help manage nausea and maintain stable blood sugar levels.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Nutrition Tip</h4>
                        <p>If morning sickness is affecting your appetite, try eating bland, dry foods like crackers before getting out of bed, and consider ginger tea to help settle your stomach.</p>
                    </div>
                </div>

                <div class="care-category">
                    <h3><i class="fas fa-running"></i> Exercise Recommendations</h3>
                    <ul class="care-list">
                        <li><strong>Low-Impact Activities:</strong> Walking, swimming, and prenatal yoga are excellent choices.</li>
                        <li><strong>Duration:</strong> Aim for 30 minutes of moderate activity most days of the week.</li>
                        <li><strong>Intensity:</strong> Keep exercise at a level where you can still hold a conversation.</li>
                        <li><strong>Pelvic Floor Exercises:</strong> Begin Kegel exercises to strengthen pelvic floor muscles.</li>
                        <li><strong>Activities to Avoid:</strong> Contact sports, activities with fall risks, and exercises that involve lying flat on your back.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Exercise Tip</h4>
                        <p>Always listen to your body and stop if you feel dizzy, short of breath, or experience any pain. Stay hydrated before, during, and after exercise.</p>
                    </div>
                </div>

                <div class="care-category">
                    <h3><i class="fas fa-heartbeat"></i> Common Symptoms & Management</h3>
                    <ul class="care-list">
                        <li><strong>Morning Sickness:</strong> Try eating small, frequent meals, ginger products, and vitamin B6 supplements (with healthcare provider approval).</li>
                        <li><strong>Fatigue:</strong> Prioritize rest, take short naps when possible, and maintain a consistent sleep schedule.</li>
                        <li><strong>Breast Tenderness:</strong> Wear a supportive, comfortable bra and avoid caffeine which can increase discomfort.</li>
                        <li><strong>Frequent Urination:</strong> Stay hydrated but reduce fluid intake before bedtime.</li>
                        <li><strong>Mood Swings:</strong> Practice stress-reduction techniques like meditation, gentle exercise, and ensure adequate rest.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Important Note</h4>
                        <p>Contact your healthcare provider immediately if you experience severe nausea and vomiting, vaginal bleeding, severe abdominal pain, or dizziness.</p>
                    </div>
                </div>
            </div>

            <div id="second-trimester" class="tab-content">
                <div class="care-category">
                    <h3><i class="fas fa-utensils"></i> Nutrition Guidelines</h3>
                    <ul class="care-list">
                        <li><strong>Increased Caloric Needs:</strong> Add approximately 300-350 extra calories daily from nutrient-dense foods.</li>
                        <li><strong>Protein:</strong> Increase intake to support baby's growth with lean meats, eggs, dairy, legumes, and nuts.</li>
                        <li><strong>Omega-3 Fatty Acids:</strong> Include fatty fish (within mercury guidelines), walnuts, and flaxseeds for baby's brain development.</li>
                        <li><strong>Vitamin D:</strong> Ensure adequate intake through sunlight exposure, fortified foods, and supplements if recommended.</li>
                        <li><strong>Fiber:</strong> Consume plenty of fruits, vegetables, and whole grains to prevent constipation.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Nutrition Tip</h4>
                        <p>This is often when appetite improves. Focus on nutrient-dense foods rather than empty calories to support your baby's rapid development.</p>
                    </div>
                </div>

                <div class="care-category">
                    <h3><i class="fas fa-running"></i> Exercise Recommendations</h3>
                    <ul class="care-list">
                        <li><strong>Modified Routines:</strong> Adjust exercises as your center of gravity shifts to prevent strain or falls.</li>
                        <li><strong>Strength Training:</strong> Light to moderate strength training can help prepare for the physical demands of later pregnancy.</li>
                        <li><strong>Swimming:</strong> Excellent for relieving pressure on joints while providing resistance.</li>
                        <li><strong>Posture Awareness:</strong> Practice good posture to minimize back pain as your abdomen grows.</li>
                        <li><strong>Warning Signs:</strong> Stop exercising if you experience dizziness, shortness of breath, contractions, or fluid leakage.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Exercise Tip</h4>
                        <p>Wear supportive footwear and a maternity support belt if needed to reduce discomfort during physical activity.</p>
                    </div>
                </div>

                <div class="care-category">
                    <h3><i class="fas fa-heartbeat"></i> Common Symptoms & Management</h3>
                    <ul class="care-list">
                        <li><strong>Back Pain:</strong> Practice good posture, use proper body mechanics when lifting, and consider prenatal massage.</li>
                        <li><strong>Leg Cramps:</strong> Stay hydrated, stretch calves regularly, and ensure adequate calcium and magnesium intake.</li>
                        <li><strong>Nasal Congestion:</strong> Use saline nasal sprays and humidifiers to alleviate pregnancy rhinitis.</li>
                        <li><strong>Skin Changes:</strong> Use gentle, fragrance-free moisturizers for stretch marks and itching; protect darkened skin from sun exposure.</li>
                        <li><strong>Heartburn:</strong> Eat smaller, more frequent meals, avoid lying down after eating, and eliminate trigger foods.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Important Note</h4>
                        <p>The second trimester is an ideal time for dental check-ups, as pregnancy can affect oral health. Inform your dentist about your pregnancy.</p>
                    </div>
                </div>
            </div>

            <div id="third-trimester" class="tab-content">
                <div class="care-category">
                    <h3><i class="fas fa-utensils"></i> Nutrition Guidelines</h3>
                    <ul class="care-list">
                        <li><strong>Increased Caloric Needs:</strong> Add approximately 450-500 extra calories daily from nutrient-dense foods.</li>
                        <li><strong>Iron-Rich Foods:</strong> Increase intake to prevent anemia with lean red meat, spinach, beans, and fortified cereals.</li>
                        <li><strong>Smaller, More Frequent Meals:</strong> Help manage heartburn and the pressure of your growing baby on your stomach.</li>
                        <li><strong>Calcium:</strong> Continue focusing on calcium-rich foods for your baby's developing bones and teeth.</li>
                        <li><strong>Hydration:</strong> Maintain adequate fluid intake to prevent contractions and support amniotic fluid levels.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Nutrition Tip</h4>
                        <p>Keep healthy snacks readily available for energy boosts throughout the day, and to maintain stable blood sugar levels.</p>
                    </div>
                </div>

                <div class="care-category">
                    <h3><i class="fas fa-running"></i> Exercise Recommendations</h3>
                    <ul class="care-list">
                        <li><strong>Low-Impact Activities:</strong> Focus on walking, swimming, and prenatal yoga with modifications.</li>
                        <li><strong>Pelvic Tilts:</strong> Help relieve back pain and prepare for labor.</li>
                        <li><strong>Kegel Exercises:</strong> Continue strengthening pelvic floor muscles to prepare for delivery and recovery.</li>
                        <li><strong>Stretching:</strong> Gentle stretching can help with discomfort and maintain flexibility.</li>
                        <li><strong>Rest Periods:</strong> Include adequate rest between activities as fatigue increases.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Exercise Tip</h4>
                        <p>Consider water exercises which can be particularly comfortable in the third trimester as they reduce pressure on your joints and back.</p>
                    </div>
                </div>

                <div class="care-category">
                    <h3><i class="fas fa-heartbeat"></i> Common Symptoms & Management</h3>
                    <ul class="care-list">
                        <li><strong>Shortness of Breath:</strong> Practice good posture, use pillows to elevate upper body when sleeping, and avoid overexertion.</li>
                        <li><strong>Swelling:</strong> Elevate feet when sitting, avoid standing for long periods, and wear compression stockings if recommended.</li>
                        <li><strong>Sleep Difficulties:</strong> Use pregnancy pillows, sleep on your left side, and establish a relaxing bedtime routine.</li>
                        <li><strong>Braxton Hicks Contractions:</strong> Stay hydrated, change positions, and practice relaxation techniques.</li>
                        <li><strong>Frequent Urination:</strong> Continue to stay hydrated despite the inconvenience, but limit fluids before bedtime.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Important Note</h4>
                        <p>Contact your healthcare provider immediately if you experience severe headaches, vision changes, sudden swelling, decreased fetal movement, or regular contractions before 37 weeks.</p>
                    </div>
                </div>

                <div class="care-category">
                    <h3><i class="fas fa-hospital"></i> Birth Preparation</h3>
                    <ul class="care-list">
                        <li><strong>Birth Plan:</strong> Develop a flexible birth plan outlining your preferences for labor and delivery.</li>
                        <li><strong>Hospital Bag:</strong> Pack essentials for yourself, your partner, and your newborn by 36 weeks.</li>
                        <li><strong>Childbirth Education:</strong> Complete childbirth classes to learn coping techniques for labor.</li>
                        <li><strong>Pediatrician Selection:</strong> Research and select a pediatrician for your baby before delivery.</li>
                        <li><strong>Support System:</strong> Arrange for help during the first few weeks after birth for recovery and newborn care.</li>
                    </ul>
                    <div class="care-tip">
                        <h4>Preparation Tip</h4>
                        <p>Consider preparing and freezing meals ahead of time for easy nutrition during the early postpartum period.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="infant-care.html">Infant Care</a></li>
                        <li><a href="tools.html">Tools</a></li>
                        <li><a href="community.html">Community</a></li>
                        <li><a href="blog.html">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="tools.html#due-date">Due Date Calculator</a></li>
                        <li><a href="tools.html#bmi">BMI Tracker</a></li>
                        <li><a href="tools.html#growth-tracker">Growth Tracker</a></li>
                        <li><a href="tools.html#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <script src="../js/logout.js"></script>
    <script>
        // Trimester tabs functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Show corresponding content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });


        });
    </script>
</body>
</html>
