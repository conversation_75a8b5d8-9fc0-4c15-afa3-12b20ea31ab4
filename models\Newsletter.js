const mongoose = require('mongoose');

const NewsletterSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, 'Please add an email'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please add a valid email'
    ]
  },
  isSubscribed: {
    type: Boolean,
    default: true
  },
  subscribedAt: {
    type: Date,
    default: Date.now
  },
  unsubscribedAt: {
    type: Date,
    default: null
  },
  unsubscribeToken: {
    type: String,
    default: null
  },
  topics: {
    type: [String],
    default: ['all']
  }
}, {
  timestamps: true
});

// Set indexes for better performance
NewsletterSchema.index({ email: 1 });
NewsletterSchema.index({ unsubscribeToken: 1 });

module.exports = mongoose.model('Newsletter', NewsletterSchema);
