<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infant Care Guidance | MATERNIFY</title>
    <meta name="description" content="Monthly growth milestones, newborn care tips, and infant health advice for new parents.">
    <link rel="stylesheet" href="../css/styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        /* Page-specific styles */
        .age-selector {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
        }

        .age-button {
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--light-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-md);
            font-family: var(--heading-font);
            font-weight: 500;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .age-button:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .age-button.active {
            background-color: var(--secondary-color);
            border-color: var(--secondary-dark);
            color: var(--text-color);
        }

        .milestone-content {
            display: none;
        }

        .milestone-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .milestone-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .milestone-card {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }

        .milestone-header {
            background-color: var(--secondary-color);
            padding: var(--spacing-md);
            text-align: center;
        }

        .milestone-header h3 {
            margin-bottom: 0;
            color: var(--text-color);
        }

        .milestone-body {
            padding: var(--spacing-lg);
        }

        .milestone-category {
            margin-bottom: var(--spacing-md);
        }

        .milestone-category h4 {
            color: var(--secondary-dark);
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .milestone-category h4 i {
            margin-right: var(--spacing-sm);
        }

        .milestone-list {
            list-style-type: none;
        }

        .milestone-list li {
            margin-bottom: var(--spacing-sm);
            padding-left: var(--spacing-lg);
            position: relative;
        }

        .milestone-list li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 10px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--secondary-color);
        }

        .care-tips {
            background-color: var(--light-bg);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            margin-top: var(--spacing-lg);
        }

        .care-tips h3 {
            color: var(--secondary-dark);
            margin-bottom: var(--spacing-md);
            text-align: center;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
        }

        .tip-card {
            background-color: var(--white);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
        }

        .tip-card h4 {
            color: var(--secondary-dark);
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .tip-card h4 i {
            margin-right: var(--spacing-sm);
        }

        .warning-note {
            background-color: var(--warning-color);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            margin-top: var(--spacing-lg);
        }

        .warning-note h4 {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }

        .warning-note h4 i {
            margin-right: var(--spacing-sm);
            color: #e67e22;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="../index.html">Home</a></li>
                    <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                    <li><a href="infant-care.html" class="active">Infant Care</a></li>
                    <li><a href="tools.html">Tools</a></li>
                    <li><a href="community.html">Community</a></li>
                    <li><a href="blog.html">Articles</a></li>
                    <li><a href="login.html" class="btn-login">Login</a></li>
                    <li><a href="register.html" class="btn-register">Register</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Infant Care Guidance</h1>
            <p>Monthly growth milestones and essential care tips for your baby's first year.</p>
        </div>
    </section>

    <section class="infant-care">
        <div class="container">
            <div class="age-selector">
                <button class="age-button active" data-age="newborn">Newborn (0-1 month)</button>
                <button class="age-button" data-age="month-2">2 Months</button>
                <button class="age-button" data-age="month-3">3 Months</button>
                <button class="age-button" data-age="month-4">4 Months</button>
                <button class="age-button" data-age="month-6">6 Months</button>
                <button class="age-button" data-age="month-9">9 Months</button>
                <button class="age-button" data-age="month-12">12 Months</button>
            </div>

            <div id="newborn" class="milestone-content active">
                <div class="milestone-grid">
                    <div class="milestone-card">
                        <div class="milestone-header">
                            <h3>Newborn (0-1 month)</h3>
                        </div>
                        <div class="milestone-body">
                            <div class="milestone-category">
                                <h4><i class="fas fa-brain"></i> Development Milestones</h4>
                                <ul class="milestone-list">
                                    <li>Focuses on objects 8-12 inches away</li>
                                    <li>Follows moving objects with eyes</li>
                                    <li>Recognizes familiar voices, especially parents'</li>
                                    <li>Turns head toward sounds</li>
                                    <li>Lifts head briefly during tummy time</li>
                                    <li>Makes reflexive movements</li>
                                </ul>
                            </div>
                            <div class="milestone-category">
                                <h4><i class="fas fa-utensils"></i> Feeding Patterns</h4>
                                <ul class="milestone-list">
                                    <li>Feeds 8-12 times per day (every 2-3 hours)</li>
                                    <li>Breastfed babies: 1.5-3 oz per feeding</li>
                                    <li>Formula-fed babies: 2-4 oz per feeding</li>
                                    <li>May lose up to 10% of birth weight initially, but should regain it by 2 weeks</li>
                                </ul>
                            </div>
                            <div class="milestone-category">
                                <h4><i class="fas fa-bed"></i> Sleep Patterns</h4>
                                <ul class="milestone-list">
                                    <li>Sleeps 14-17 hours per day</li>
                                    <li>Short sleep cycles of 2-4 hours</li>
                                    <li>No established day/night pattern yet</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="care-tips">
                    <h3>Essential Newborn Care Tips</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <h4><i class="fas fa-bath"></i> Bathing</h4>
                            <p>Sponge bathe until umbilical cord falls off (usually 1-2 weeks). Keep cord area clean and dry. After cord falls off, bathe 2-3 times per week with mild, fragrance-free soap.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-baby"></i> Diapering</h4>
                            <p>Change diapers every 2-3 hours or as needed. Clean area thoroughly with water or gentle wipes. Apply diaper cream if redness appears. Allow diaper-free time to air out skin.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-head-side-cough"></i> Soothing Techniques</h4>
                            <p>Try the 5 S's: Swaddle, Side/Stomach position (while awake), Shushing, Swinging, and Sucking. Establish a calming routine with gentle rocking, white noise, or soft singing.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-bed"></i> Safe Sleep</h4>
                            <p>Always place baby on back to sleep on firm, flat surface. Keep crib free of blankets, pillows, toys, and bumpers. Room-share but don't bed-share. Maintain comfortable room temperature (68-72°F).</p>
                        </div>
                    </div>

                    <div class="warning-note">
                        <h4><i class="fas fa-exclamation-triangle"></i> When to Call the Doctor</h4>
                        <p>Contact your pediatrician immediately if your newborn has:</p>
                        <ul>
                            <li>Fever (rectal temperature of 100.4°F/38°C or higher)</li>
                            <li>Difficulty breathing or rapid breathing</li>
                            <li>Yellowing of the skin or eyes (jaundice)</li>
                            <li>Refusal to feed for multiple sessions</li>
                            <li>Fewer than 6 wet diapers in 24 hours</li>
                            <li>Unusual fussiness or lethargy</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="month-2" class="milestone-content">
                <div class="milestone-grid">
                    <div class="milestone-card">
                        <div class="milestone-header">
                            <h3>2 Months</h3>
                        </div>
                        <div class="milestone-body">
                            <div class="milestone-category">
                                <h4><i class="fas fa-brain"></i> Development Milestones</h4>
                                <ul class="milestone-list">
                                    <li>Begins to smile socially in response to others</li>
                                    <li>Makes cooing sounds and responds to voices</li>
                                    <li>Follows objects with eyes across midline</li>
                                    <li>Holds head up more steadily during tummy time</li>
                                    <li>May briefly hold a rattle when placed in hand</li>
                                    <li>Begins to discover hands and may bring them together</li>
                                </ul>
                            </div>
                            <div class="milestone-category">
                                <h4><i class="fas fa-utensils"></i> Feeding Patterns</h4>
                                <ul class="milestone-list">
                                    <li>Feeds 7-9 times per day</li>
                                    <li>Breastfed babies: 3-4 oz per feeding</li>
                                    <li>Formula-fed babies: 4-5 oz per feeding</li>
                                    <li>Typically gains 1-1.5 pounds per month</li>
                                </ul>
                            </div>
                            <div class="milestone-category">
                                <h4><i class="fas fa-bed"></i> Sleep Patterns</h4>
                                <ul class="milestone-list">
                                    <li>Sleeps 14-16 hours per day</li>
                                    <li>May have longer stretches at night (3-4 hours)</li>
                                    <li>Beginning to develop day/night awareness</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="care-tips">
                    <h3>2-Month Care Tips</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <h4><i class="fas fa-comments"></i> Communication</h4>
                            <p>Talk, sing, and read to your baby frequently. Respond to their coos and babbles to encourage language development. Make eye contact during interactions.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-dumbbell"></i> Tummy Time</h4>
                            <p>Aim for 3-5 short sessions daily (3-5 minutes each). Place colorful toys within reach to encourage reaching and head lifting. Always supervise tummy time.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-syringe"></i> Vaccinations</h4>
                            <p>Prepare for 2-month vaccinations. You can give infant acetaminophen if recommended by your pediatrician for post-vaccination discomfort.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-clock"></i> Routine Development</h4>
                            <p>Begin establishing consistent feeding and sleeping routines. Create a simple bedtime ritual with calming activities like bathing, gentle massage, and quiet time.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div id="month-3" class="milestone-content">
                <div class="milestone-grid">
                    <div class="milestone-card">
                        <div class="milestone-header">
                            <h3>3 Months</h3>
                        </div>
                        <div class="milestone-body">
                            <div class="milestone-category">
                                <h4><i class="fas fa-brain"></i> Development Milestones</h4>
                                <ul class="milestone-list">
                                    <li>Smiles and laughs in response to others</li>
                                    <li>Recognizes familiar faces and objects</li>
                                    <li>Raises head and chest during tummy time</li>
                                    <li>Opens and closes hands</li>
                                    <li>Swipes at dangling objects</li>
                                    <li>Makes more varied sounds (babbling begins)</li>
                                </ul>
                            </div>
                            <div class="milestone-category">
                                <h4><i class="fas fa-utensils"></i> Feeding Patterns</h4>
                                <ul class="milestone-list">
                                    <li>Feeds 6-8 times per day</li>
                                    <li>Breastfed babies: 4-5 oz per feeding</li>
                                    <li>Formula-fed babies: 5-6 oz per feeding</li>
                                    <li>May begin to drool more as salivary glands develop</li>
                                </ul>
                            </div>
                            <div class="milestone-category">
                                <h4><i class="fas fa-bed"></i> Sleep Patterns</h4>
                                <ul class="milestone-list">
                                    <li>Sleeps 14-15 hours per day</li>
                                    <li>May sleep 5-6 hour stretches at night</li>
                                    <li>Takes 3-4 naps during the day</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="care-tips">
                    <h3>3-Month Care Tips</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <h4><i class="fas fa-gamepad"></i> Play & Stimulation</h4>
                            <p>Introduce age-appropriate toys with different textures, colors, and sounds. Use mirrors during play. Narrate daily activities to expose baby to language.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-baby"></i> Motor Skills</h4>
                            <p>Encourage reaching and grasping by placing toys within reach. Continue regular tummy time to strengthen neck, back, and shoulder muscles.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-bed"></i> Sleep Training</h4>
                            <p>Consider introducing a more consistent sleep schedule. Begin putting baby down drowsy but awake to help them learn to self-soothe.</p>
                        </div>
                        <div class="tip-card">
                            <h4><i class="fas fa-heart"></i> Social Development</h4>
                            <p>Respond promptly to cries to build trust and security. Engage in face-to-face interaction with lots of smiling and talking.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional age milestones would be added here following the same pattern -->

            <div id="month-4" class="milestone-content">
                <!-- Content for 4 months -->
                <p class="text-center" style="text-align: center; padding: var(--spacing-xl) 0;">Content for 4-month milestones will be added soon.</p>
            </div>

            <div id="month-6" class="milestone-content">
                <!-- Content for 6 months -->
                <p class="text-center" style="text-align: center; padding: var(--spacing-xl) 0;">Content for 6-month milestones will be added soon.</p>
            </div>

            <div id="month-9" class="milestone-content">
                <!-- Content for 9 months -->
                <p class="text-center" style="text-align: center; padding: var(--spacing-xl) 0;">Content for 9-month milestones will be added soon.</p>
            </div>

            <div id="month-12" class="milestone-content">
                <!-- Content for 12 months -->
                <p class="text-center" style="text-align: center; padding: var(--spacing-xl) 0;">Content for 12-month milestones will be added soon.</p>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="infant-care.html">Infant Care</a></li>
                        <li><a href="tools.html">Tools</a></li>
                        <li><a href="community.html">Community</a></li>
                        <li><a href="blog.html">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="tools.html#due-date">Due Date Calculator</a></li>
                        <li><a href="tools.html#bmi">BMI Tracker</a></li>
                        <li><a href="tools.html#growth-tracker">Growth Tracker</a></li>
                        <li><a href="tools.html#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <script>
        // Age selector functionality
        document.addEventListener('DOMContentLoaded', function() {
            const ageButtons = document.querySelectorAll('.age-button');
            const milestoneContents = document.querySelectorAll('.milestone-content');

            ageButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and contents
                    ageButtons.forEach(btn => btn.classList.remove('active'));
                    milestoneContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Show corresponding content
                    const ageId = this.getAttribute('data-age');
                    document.getElementById(ageId).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
