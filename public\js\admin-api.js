/**
 * Admin API Service for MATERNIFY
 * This service handles all API calls for the admin dashboard
 */

class AdminApiService {
  constructor() {
    // Use the current host with port 8080
    this.baseUrl = window.location.protocol + '//' + window.location.hostname + ':8080/api';
    this.token = localStorage.getItem('token');

    // For local development, if already on port 8080, don't add it again
    if (window.location.port === '8080') {
      this.baseUrl = '/api';
    }
  }

  /**
   * Set the authentication token
   * @param {string} token - JWT token
   */
  setToken(token) {
    this.token = token;
    localStorage.setItem('token', token);
  }

  /**
   * Clear the authentication token
   */
  clearToken() {
    this.token = null;
    localStorage.removeItem('token');
  }

  /**
   * Get headers for API requests
   * @returns {Object} Headers object
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make an API request
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} data - Request data
   * @returns {Promise} Promise with response data
   */
  async request(endpoint, method = 'GET', data = null) {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const options = {
        method,
        headers: this.getHeaders(),
      };

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(url, options);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'API request failed');
      }

      return result;
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Dashboard endpoints
  async getDashboardStats() {
    return this.request('/dashboard/stats');
  }

  // User endpoints
  async getAllUsers(params = {}) {
    const queryParams = new URLSearchParams(params).toString();
    return this.request(`/users?${queryParams}`);
  }

  async getUserById(id) {
    return this.request(`/users/${id}`);
  }

  async createUser(userData) {
    return this.request('/users', 'POST', userData);
  }

  async updateUser(id, userData) {
    return this.request(`/users/${id}`, 'PATCH', userData);
  }

  async deleteUser(id) {
    return this.request(`/users/${id}`, 'DELETE');
  }

  // Content endpoints
  async getAllContent(params = {}) {
    const queryParams = new URLSearchParams(params).toString();
    return this.request(`/content?${queryParams}`);
  }

  async getContentById(id) {
    return this.request(`/content/${id}`);
  }

  async createContent(contentData) {
    return this.request('/content', 'POST', contentData);
  }

  async updateContent(id, contentData) {
    return this.request(`/content/${id}`, 'PATCH', contentData);
  }

  async deleteContent(id) {
    return this.request(`/content/${id}`, 'DELETE');
  }

  // Activity endpoints
  async getAllActivities(params = {}) {
    const queryParams = new URLSearchParams(params).toString();
    return this.request(`/activities?${queryParams}`);
  }

  async getActivityStats() {
    return this.request('/activities/stats');
  }

  // System log endpoints
  async getAllLogs(params = {}) {
    const queryParams = new URLSearchParams(params).toString();
    return this.request(`/logs?${queryParams}`);
  }

  async getLogStats() {
    return this.request('/logs/stats');
  }

  // Settings endpoints
  async getAllSettings(params = {}) {
    const queryParams = new URLSearchParams(params).toString();
    return this.request(`/settings?${queryParams}`);
  }

  async getSettingByKey(key) {
    return this.request(`/settings/${key}`);
  }

  async updateSetting(key, value) {
    return this.request(`/settings/${key}`, 'PATCH', { value });
  }

  async updateMultipleSettings(settings) {
    return this.request('/settings', 'PATCH', { settings });
  }
}

// Create and export the admin API service instance
const adminApiService = new AdminApiService();
