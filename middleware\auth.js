const jwt = require('jsonwebtoken');
const User = require('../models/User');
const fs = require('fs');

// Function to check if MongoDB is available
const isMongoDBAvailable = () => {
  try {
    // Check mongoose connection state
    // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting
    return User.db.readyState === 1;
  } catch (error) {
    fs.appendFileSync('server-log.txt', `Auth middleware error checking MongoDB: ${error.message}\n`);
    return false;
  }
};

// Protect routes
exports.protect = async (req, res, next) => {
  let token;

  try {
    // Get token from authorization header or cookie
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      // Set token from Bearer token in header
      token = req.headers.authorization.split(' ')[1];
    } else if (req.cookies && req.cookies.token) {
      // Set token from cookie
      token = req.cookies.token;
    }

    // Make sure token exists
    if (!token) {
      fs.appendFileSync('server-log.txt', 'Authentication failed: No token provided\n');
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
        code: 'NO_TOKEN'
      });
    }

    // Verify token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || 'maternify_secret_key');
    } catch (error) {
      // Handle different JWT verification errors
      if (error.name === 'TokenExpiredError') {
        fs.appendFileSync('server-log.txt', 'Authentication failed: Token expired\n');
        return res.status(401).json({
          success: false,
          message: 'Token expired, please log in again',
          code: 'TOKEN_EXPIRED'
        });
      } else if (error.name === 'JsonWebTokenError') {
        fs.appendFileSync('server-log.txt', `Authentication failed: Invalid token - ${error.message}\n`);
        return res.status(401).json({
          success: false,
          message: 'Invalid token, please log in again',
          code: 'INVALID_TOKEN'
        });
      } else {
        fs.appendFileSync('server-log.txt', `Authentication failed: ${error.message}\n`);
        return res.status(401).json({
          success: false,
          message: 'Authentication failed, please log in again',
          code: 'AUTH_FAILED'
        });
      }
    }

    // Log token verification
    fs.appendFileSync('server-log.txt', `Token verified for user id: ${decoded.id}\n`);

    // Check if MongoDB is available
    const mongoAvailable = isMongoDBAvailable();

    // If MongoDB is not available, use fallback mode
    if (!mongoAvailable) {
      fs.appendFileSync('server-log.txt', 'Using fallback mode for authentication\n');

      // For demonstration purposes, set a mock user
      req.user = {
        id: decoded.id,
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'user'
      };

      return next();
    }

    // MongoDB is available, proceed with normal authentication
    req.user = await User.findById(decoded.id);

    if (!req.user) {
      fs.appendFileSync('server-log.txt', `Authentication failed: User with id ${decoded.id} not found\n`);
      return res.status(401).json({
        success: false,
        message: 'User no longer exists',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check if user is active
    if (req.user.active === false) {
      fs.appendFileSync('server-log.txt', `Authentication failed: User with id ${decoded.id} is inactive\n`);
      return res.status(401).json({
        success: false,
        message: 'User account is inactive',
        code: 'USER_INACTIVE'
      });
    }

    fs.appendFileSync('server-log.txt', `Authentication successful for user id: ${decoded.id}\n`);

    next();
  } catch (err) {
    fs.appendFileSync('server-log.txt', `Authentication error: ${err.message}\n`);
    return res.status(500).json({
      success: false,
      message: 'Server error during authentication',
      code: 'SERVER_ERROR'
    });
  }
};

// Grant access to specific roles
exports.authorize = (...roles) => {
  return (req, res, next) => {
    try {
      // Check if user object exists
      if (!req.user) {
        fs.appendFileSync('server-log.txt', 'Authorization failed: No user object found\n');
        return res.status(401).json({
          success: false,
          message: 'User not authenticated',
          code: 'USER_NOT_AUTHENTICATED'
        });
      }

      // Check if user has required role
      if (!roles.includes(req.user.role)) {
        fs.appendFileSync('server-log.txt', `Authorization failed: User role ${req.user.role} not authorized\n`);
        return res.status(403).json({
          success: false,
          message: `User role ${req.user.role} is not authorized to access this route`,
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      fs.appendFileSync('server-log.txt', `Authorization successful for user role: ${req.user.role}\n`);
      next();
    } catch (error) {
      fs.appendFileSync('server-log.txt', `Authorization error: ${error.message}\n`);
      return res.status(500).json({
        success: false,
        message: 'Server error during authorization',
        code: 'SERVER_ERROR'
      });
    }
  };
};
