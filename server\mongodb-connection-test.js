/**
 * MongoDB Connection Test Script
 *
 * This script tests the connection to MongoDB using the connection string in your .env file.
 */

require('dotenv').config({ path: __dirname + '/.env' });
const mongoose = require('mongoose');

console.log('\n=== MongoDB Connection Test ===\n');

// Get the MongoDB URI from the environment variables
const mongoURI = process.env.MONGODB_URI;

if (!mongoURI) {
  console.error('Error: MONGODB_URI is not defined in your .env file');
  process.exit(1);
}

// Display the connection string (with password masked)
const maskedURI = mongoURI.replace(/:([^@]+)@/, ':********@');
console.log(`Attempting to connect to MongoDB with URI: ${maskedURI}\n`);

// Connect to MongoDB
mongoose.connect(mongoURI)
  .then(() => {
    console.log('✅ MongoDB connection successful!');
    console.log(`Connected to: ${mongoose.connection.host}`);
    console.log(`Database name: ${mongoose.connection.name}`);

    // List all collections in the database
    return mongoose.connection.db.listCollections().toArray();
  })
  .then(collections => {
    if (collections.length === 0) {
      console.log('\nNo collections found in the database.');
    } else {
      console.log('\nCollections in the database:');
      collections.forEach(collection => {
        console.log(`- ${collection.name}`);
      });
    }

    console.log('\nConnection test completed successfully.');

    // Close the connection
    return mongoose.connection.close();
  })
  .then(() => {
    console.log('MongoDB connection closed.');
    process.exit(0);
  })
  .catch(err => {
    console.error('\n❌ MongoDB connection failed!');
    console.error(`Error: ${err.message}`);

    // Provide more specific error messages based on error type
    if (err.name === 'MongoParseError') {
      console.error('\nThe MongoDB connection string appears to be invalid. Please check your MONGODB_URI in the .env file.');
    } else if (err.name === 'MongoNetworkError') {
      console.error('\nA network error occurred. Please check:');
      console.error('1. Your internet connection');
      console.error('2. If you\'re using MongoDB Atlas, check if your IP address is whitelisted in Network Access');
      console.error('3. If you\'re using a local MongoDB, check if the MongoDB service is running');
    } else if (err.name === 'MongoServerSelectionError') {
      console.error('\nCould not connect to any MongoDB server. Please check:');
      console.error('1. If you\'re using MongoDB Atlas, check if your cluster is running');
      console.error('2. If you\'re using a local MongoDB, check if the MongoDB service is running on the specified port');
    } else if (err.name === 'MongoError' && err.code === 18) {
      console.error('\nAuthentication failed. Please check your username and password in the connection string.');
    }

    console.error('\nFor MongoDB Atlas users:');
    console.error('1. Make sure you\'ve created a database user with the correct username and password');
    console.error('2. Make sure you\'ve whitelisted your IP address in the Network Access settings');
    console.error('3. Make sure you\'ve correctly replaced <username>, <password>, and <clustername> in your connection string');

    process.exit(1);
  });
