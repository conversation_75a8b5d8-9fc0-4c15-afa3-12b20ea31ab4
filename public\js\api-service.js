/**
 * API Service for MATERNIFY
 * Handles all API requests to the backend
 */

class ApiService {
  constructor() {
    // Use the current origin (protocol, hostname, and port) for API requests
    // This ensures we use the same port the page is being served from
    this.baseUrl = '/api';
    this.token = localStorage.getItem('token');
    this.refreshToken = localStorage.getItem('refreshToken');
    this.refreshing = false;
    this.refreshQueue = [];

    // Set up token expiration check
    this.setupTokenExpirationCheck();
  }

  /**
   * Set up token expiration check
   * Checks if the token is about to expire and refreshes it
   */
  setupTokenExpirationCheck() {
    // Check token expiration every minute
    setInterval(() => {
      if (this.token) {
        try {
          // Check if token is about to expire (within 5 minutes)
          const tokenData = this.parseJwt(this.token);
          const expirationTime = tokenData.exp * 1000; // Convert to milliseconds
          const currentTime = Date.now();
          const timeUntilExpiration = expirationTime - currentTime;

          // If token expires in less than 5 minutes and we have a refresh token, refresh it
          if (timeUntilExpiration < 5 * 60 * 1000 && this.refreshToken) {
            console.log('Token is about to expire, refreshing...');
            this.refreshAccessToken();
          }
        } catch (error) {
          console.error('Error checking token expiration:', error);
        }
      }
    }, 60000); // Check every minute
  }

  /**
   * Parse JWT token to get payload
   * @param {string} token - JWT token
   * @returns {Object} - Decoded token payload
   */
  parseJwt(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error parsing JWT:', error);
      return {};
    }
  }

  /**
   * Set the authentication token
   * @param {string} token - JWT token
   * @param {string} refreshToken - Refresh token (optional)
   */
  setToken(token, refreshToken) {
    this.token = token;
    localStorage.setItem('token', token);

    if (refreshToken) {
      this.refreshToken = refreshToken;
      localStorage.setItem('refreshToken', refreshToken);
    }
  }

  /**
   * Clear the authentication tokens
   */
  clearToken() {
    this.token = null;
    this.refreshToken = null;
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }

  /**
   * Refresh the access token using the refresh token
   * @returns {Promise<boolean>} - Promise resolving to true if refresh was successful
   */
  async refreshAccessToken() {
    // If already refreshing, wait for it to complete
    if (this.refreshing) {
      return new Promise((resolve) => {
        this.refreshQueue.push(resolve);
      });
    }

    this.refreshing = true;

    try {
      if (!this.refreshToken) {
        throw new Error('No refresh token available');
      }

      console.log('Refreshing access token...');

      const response = await fetch(`${this.baseUrl}/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refreshToken: this.refreshToken })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to refresh token');
      }

      // Update tokens
      this.token = data.accessToken;
      this.refreshToken = data.refreshToken;

      // Store tokens
      localStorage.setItem('token', data.accessToken);
      localStorage.setItem('refreshToken', data.refreshToken);

      // If user data is returned, update it
      if (data.user) {
        localStorage.setItem('user', JSON.stringify(data.user));
        if (data.user.firstName) {
          localStorage.setItem('userFirstName', data.user.firstName);
        }
        if (data.user.lastName) {
          localStorage.setItem('userLastName', data.user.lastName);
        }
      }

      console.log('Token refresh successful');

      // Resolve all queued promises
      this.refreshQueue.forEach(resolve => resolve(true));
      this.refreshQueue = [];

      this.refreshing = false;
      return true;
    } catch (error) {
      console.error('Error refreshing token:', error);

      // If refresh fails, clear tokens and user data
      this.clearToken();
      localStorage.removeItem('user');
      localStorage.removeItem('userFirstName');
      localStorage.removeItem('userLastName');

      // Reject all queued promises
      this.refreshQueue.forEach(resolve => resolve(false));
      this.refreshQueue = [];

      this.refreshing = false;
      return false;
    }
  }

  /**
   * Get the authentication headers
   * @returns {Object} - Headers object with Authorization if token exists
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make a GET request
   * @param {string} endpoint - API endpoint
   * @param {boolean} retry - Whether this is a retry after token refresh
   * @returns {Promise} - Promise with response data
   */
  async get(endpoint, retry = false) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        headers: this.getHeaders()
      });

      // If response is 401 Unauthorized and we have a refresh token, try to refresh the token
      if (response.status === 401 && this.refreshToken && !retry) {
        console.log('Token expired, attempting to refresh...');
        const refreshed = await this.refreshAccessToken();

        if (refreshed) {
          // Retry the request with the new token
          return this.get(endpoint, true);
        }
      }

      return await this.handleResponse(response);
    } catch (error) {
      console.error(`GET ${endpoint} error:`, error);
      throw error;
    }
  }

  /**
   * Make a POST request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request body data
   * @param {boolean} retry - Whether this is a retry after token refresh
   * @returns {Promise} - Promise with response data
   */
  async post(endpoint, data, retry = false) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });

      // If response is 401 Unauthorized and we have a refresh token, try to refresh the token
      // Skip for login and register endpoints
      if (response.status === 401 && this.refreshToken && !retry &&
          !endpoint.includes('/login') && !endpoint.includes('/register')) {
        console.log('Token expired, attempting to refresh...');
        const refreshed = await this.refreshAccessToken();

        if (refreshed) {
          // Retry the request with the new token
          return this.post(endpoint, data, true);
        }
      }

      return await this.handleResponse(response);
    } catch (error) {
      console.error(`POST ${endpoint} error:`, error);
      throw error;
    }
  }

  /**
   * Make a PUT request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request body data
   * @param {boolean} retry - Whether this is a retry after token refresh
   * @returns {Promise} - Promise with response data
   */
  async put(endpoint, data, retry = false) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });

      // If response is 401 Unauthorized and we have a refresh token, try to refresh the token
      if (response.status === 401 && this.refreshToken && !retry) {
        console.log('Token expired, attempting to refresh...');
        const refreshed = await this.refreshAccessToken();

        if (refreshed) {
          // Retry the request with the new token
          return this.put(endpoint, data, true);
        }
      }

      return await this.handleResponse(response);
    } catch (error) {
      console.error(`PUT ${endpoint} error:`, error);
      throw error;
    }
  }

  /**
   * Make a DELETE request
   * @param {string} endpoint - API endpoint
   * @param {boolean} retry - Whether this is a retry after token refresh
   * @returns {Promise} - Promise with response data
   */
  async delete(endpoint, retry = false) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers: this.getHeaders()
      });

      // If response is 401 Unauthorized and we have a refresh token, try to refresh the token
      if (response.status === 401 && this.refreshToken && !retry) {
        console.log('Token expired, attempting to refresh...');
        const refreshed = await this.refreshAccessToken();

        if (refreshed) {
          // Retry the request with the new token
          return this.delete(endpoint, true);
        }
      }

      return await this.handleResponse(response);
    } catch (error) {
      console.error(`DELETE ${endpoint} error:`, error);
      throw error;
    }
  }

  /**
   * Handle API response
   * @param {Response} response - Fetch response object
   * @returns {Promise} - Promise with response data
   */
  async handleResponse(response) {
    try {
      // Check content type to ensure we're getting JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('Received non-JSON response:', contentType);
        console.error('Response URL:', response.url);
        console.error('Response status:', response.status);

        // Try to get the text response for debugging
        const textResponse = await response.text();
        console.error('Response text (first 100 chars):', textResponse.substring(0, 100));

        throw new Error('Server returned non-JSON response. Please check server logs.');
      }

      const data = await response.json();

      if (!response.ok) {
        // If unauthorized and not a login/register request, clear token
        if (response.status === 401 &&
            !response.url.includes('/login') &&
            !response.url.includes('/register')) {
          this.clearToken();
        }

        throw new Error(data.message || 'Something went wrong');
      }

      return data;
    } catch (error) {
      console.error('Error handling API response:', error);

      // If it's a JSON parsing error, provide a more helpful message
      if (error.name === 'SyntaxError') {
        throw new Error('Invalid JSON response from server. The server might be returning HTML instead of JSON.');
      }

      throw error;
    }
  }

  // Authentication methods

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise} - Promise with response data
   */
  async register(userData) {
    console.log('Registering user with data:', { ...userData, password: '***' });

    try {
      const result = await this.post('/register', userData);

      // Important: We don't set the token or store user data here anymore
      // This ensures the user must log in after registration

      // We only store minimal data for the login page to use
      if (result.success) {
        // Store user preferences for future use after login
        if (userData.preferences) {
          const preferences = {
            newsletter: userData.preferences.newsletter || false,
            smsUpdates: userData.preferences.smsUpdates || false
          };

          // Store preferences temporarily - will be properly associated with user after login
          sessionStorage.setItem('tempUserPreferences', JSON.stringify(preferences));
        }
      }

      return result;
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: error.message || 'An error occurred during registration. Please try again.'
      };
    }
  }

  /**
   * Login a user
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise} - Promise with response data
   */
  async login(email, password, options = {}) {
    try {
      console.log('Attempting to login with email:', email);

      const result = await this.post('/login', {
        email,
        password,
        rememberMe: options.rememberMe || false,
        stayLoggedIn: options.stayLoggedIn || false
      });

      console.log('Login API response:', result);

      if (result.success && result.token) {
        // Store both access token and refresh token
        this.setToken(result.token, result.refreshToken);

        // Store user data in localStorage for dashboard use
        if (result.user) {
          localStorage.setItem('user', JSON.stringify(result.user));

          // Store user name components separately for easy access
          if (result.user.firstName) {
            localStorage.setItem('userFirstName', result.user.firstName);
          }
          if (result.user.lastName) {
            localStorage.setItem('userLastName', result.user.lastName);
          }
        } else {
          // If no user data is returned but login was successful, create a default user
          const defaultUser = {
            firstName: 'Test',
            lastName: 'User',
            email: email
          };
          localStorage.setItem('user', JSON.stringify(defaultUser));
          localStorage.setItem('userFirstName', defaultUser.firstName);
          localStorage.setItem('userLastName', defaultUser.lastName);
        }

        console.log('Login successful, tokens stored');
        return { success: true, message: 'Login successful' };
      }

      return result;
    } catch (error) {
      console.error('Login error in API service:', error);
      return {
        success: false,
        message: error.message || 'An error occurred during login. Please try again.'
      };
    }
  }

  /**
   * Logout the current user
   * @returns {Promise} - Promise with response data
   */
  async logout() {
    try {
      // Use POST instead of GET for logout (more appropriate for state-changing operations)
      // If the server call fails, we'll still clear local data
      try {
        await this.post('/logout', {});
        console.log('Logout API call successful');
      } catch (apiError) {
        console.warn('Logout API call failed, but proceeding with local logout:', apiError);
      }

      // Clear tokens and user data regardless of API call success
      this.clearToken();

      // Clear all user-related data from localStorage
      localStorage.removeItem('user');
      localStorage.removeItem('userFirstName');
      localStorage.removeItem('userLastName');
      localStorage.removeItem('pregnancyDueDate');
      localStorage.removeItem('babyBirthdate');
      localStorage.removeItem('userPreferences');
      localStorage.removeItem('refreshToken');

      console.log('Local logout successful - all tokens and user data cleared');

      // Return success even if the API call failed
      return {
        success: true,
        message: 'Logged out successfully'
      };
    } catch (error) {
      console.error('Unexpected error during logout:', error);

      // Even if there's an unexpected error, still try to clear local data
      try {
        this.clearToken();
        localStorage.removeItem('user');
        localStorage.removeItem('userFirstName');
        localStorage.removeItem('userLastName');
        localStorage.removeItem('refreshToken');
        console.log('Cleared local data despite error');
      } catch (clearError) {
        console.error('Failed to clear local data:', clearError);
      }

      // Return success anyway since we've cleared local data
      return {
        success: true,
        message: 'Logged out locally (server logout may have failed)'
      };
    }
  }

  /**
   * Get the current user
   * @returns {Promise} - Promise with response data
   */
  async getCurrentUser() {
    try {
      // First try to get the user from localStorage if available
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        const user = JSON.parse(storedUser);
        return {
          success: true,
          data: user
        };
      }

      // If no stored user, try to get from API
      return await this.get('/me');
    } catch (error) {
      console.error('Error getting current user:', error);
      // If API call fails, try to use localStorage as fallback
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          return {
            success: true,
            data: user
          };
        } catch (parseError) {
          console.error('Error parsing stored user:', parseError);
        }
      }
      throw error;
    }
  }

  /**
   * Update user profile
   * @param {Object} profileData - Profile data to update
   * @returns {Promise} - Promise with response data
   */
  async updateProfile(profileData) {
    return await this.put('/profile', profileData);
  }

  /**
   * Update user password
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise} - Promise with response data
   */
  async updatePassword(currentPassword, newPassword) {
    return await this.put('/password', { currentPassword, newPassword });
  }

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {Promise} - Promise with response data
   */
  async forgotPassword(email) {
    return await this.post('/forgot-password', { email });
  }

  /**
   * Reset password with token
   * @param {string} token - Reset token
   * @param {string} password - New password
   * @returns {Promise} - Promise with response data
   */
  async resetPassword(token, password) {
    return await this.post(`/reset-password/${token}`, { password });
  }

  /**
   * Verify email with token
   * @param {string} token - Verification token
   * @returns {Promise} - Promise with response data
   */
  async verifyEmail(token) {
    return await this.get(`/verify-email/${token}`);
  }

  /**
   * Subscribe to newsletter
   * @param {string} email - Email to subscribe
   * @param {Array} topics - Topics to subscribe to
   * @returns {Promise} - Promise with response data
   */
  async subscribeNewsletter(email, topics = ['all']) {
    return await this.post('/subscribe', { email, topics });
  }

  /**
   * Unsubscribe from newsletter
   * @param {string} token - Unsubscribe token
   * @returns {Promise} - Promise with response data
   */
  async unsubscribeNewsletter(token) {
    return await this.get(`/unsubscribe/${token}`);
  }

  // Tool methods

  /**
   * Calculate due date
   * @param {Object} data - Due date calculation data
   * @returns {Promise} - Promise with response data
   */
  async calculateDueDate(data) {
    return await this.post('/calculate-due-date', data);
  }

  /**
   * Calculate BMI
   * @param {Object} data - BMI calculation data
   * @returns {Promise} - Promise with response data
   */
  async calculateBMI(data) {
    return await this.post('/calculate-bmi', data);
  }
}

// Create a singleton instance
const apiService = new ApiService();

// Export the instance
window.apiService = apiService;
