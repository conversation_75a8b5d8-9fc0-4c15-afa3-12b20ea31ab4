const mongoose = require('mongoose');

const contentSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, 'Content title is required'],
      trim: true,
    },
    slug: {
      type: String,
      required: [true, 'Content slug is required'],
      unique: true,
      trim: true,
      lowercase: true,
    },
    type: {
      type: String,
      required: [true, 'Content type is required'],
      enum: ['page', 'article', 'video'],
    },
    content: {
      type: String,
      required: [true, 'Content body is required'],
    },
    summary: {
      type: String,
      trim: true,
    },
    featuredImage: {
      type: String,
    },
    category: {
      type: String,
      enum: [
        'pregnancy',
        'infant-care',
        'nutrition',
        'health',
        'development',
        'breastfeeding',
        'wellness',
        'other',
      ],
      default: 'other',
    },
    tags: [String],
    author: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'draft',
    },
    publishedAt: Date,
    views: {
      type: Number,
      default: 0,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Pre-save middleware to update the updatedAt field
contentSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual populate for comments
contentSchema.virtual('comments', {
  ref: 'Comment',
  foreignField: 'content',
  localField: '_id',
});

const Content = mongoose.model('Content', contentSchema);

module.exports = Content;
