<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health Journal | MATERNIFY</title>
    <meta name="description" content="Track your pregnancy and baby's health with our digital health journal.">
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/auth.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Custom styles for header to match footer -->
    <style>
        header {
            background-color: var(--text-color) !important;
        }
        header .logo h1 {
            color: var(--white) !important;
        }
        header .nav-menu {
            background-color: var(--text-color) !important;
        }
        header .nav-menu li a {
            color: var(--white) !important;
        }
        header .mobile-menu-btn .bar {
            background-color: var(--white) !important;
        }
        .user-menu {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        .user-greeting {
            color: var(--white) !important;
        }
        .logout-btn {
            color: var(--white) !important;
        }
        .logout-btn:hover {
            color: var(--primary-color) !important;
        }
        @media (max-width: 768px) {
            .nav-menu {
                background-color: var(--text-color) !important;
            }
        }
    </style>
    <style>
        /* Dashboard Layout */
        .dashboard-layout {
            display: flex;
            min-height: calc(100vh - 70px - 300px); /* Adjust based on header and footer height */
        }

        /* Sidebar Styles */
        .dashboard-sidebar {
            width: 250px;
            background-color: var(--text-color); /* Match header color */
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: var(--spacing-md);
            position: sticky;
            top: 70px; /* Adjust based on header height */
            height: calc(100vh - 70px); /* Adjust based on header height */
            overflow-y: auto;
            transition: transform 0.3s ease;
            color: var(--white);
        }

        .sidebar-header {
            padding-bottom: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            font-size: 1.2rem;
            margin-bottom: var(--spacing-sm);
        }

        .sidebar-user {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: var(--text-color);
        }

        .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 500;
        }

        .user-role {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .sidebar-nav {
            margin-bottom: var(--spacing-lg);
        }

        .sidebar-nav-title {
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.7;
            margin-bottom: var(--spacing-sm);
        }

        .sidebar-menu {
            list-style-type: none;
        }

        .sidebar-menu-item {
            margin-bottom: var(--spacing-xs);
        }

        .sidebar-menu-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            color: var(--white);
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .sidebar-menu-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu-link.active {
            background-color: var(--primary-color);
            color: var(--text-color);
        }

        .sidebar-menu-icon {
            margin-right: var(--spacing-sm);
            width: 20px;
            text-align: center;
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: var(--spacing-md);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Main Content */
        .dashboard-main {
            flex: 1;
            padding: var(--spacing-xl);
            background-color: var(--light-bg);
        }

        /* Health Journal Styles */
        .journal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
        }

        .journal-title {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-xs);
        }

        .journal-subtitle {
            font-size: 1rem;
            color: var(--text-light);
        }

        .journal-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .journal-container {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .journal-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: var(--spacing-lg);
        }

        .journal-tab {
            padding: var(--spacing-md) var(--spacing-lg);
            cursor: pointer;
            border-bottom: 3px solid transparent;
            font-weight: 500;
            color: var(--text-light);
            transition: all 0.3s ease;
        }

        .journal-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .journal-tab:hover:not(.active) {
            color: var(--text-color);
            border-bottom-color: var(--border-color);
        }

        .journal-content {
            margin-bottom: var(--spacing-lg);
        }

        .journal-form {
            margin-bottom: var(--spacing-lg);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: var(--spacing-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            font-family: var(--body-font);
            transition: border-color 0.3s ease;
        }

        .form-group textarea {
            min-height: 150px;
            resize: vertical;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: var(--spacing-sm);
        }

        .journal-entries {
            margin-top: var(--spacing-xl);
        }

        .journal-entry {
            background-color: var(--light-bg);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
            border-left: 4px solid var(--primary-color);
        }

        .journal-entry-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-sm);
        }

        .journal-entry-date {
            font-size: 0.9rem;
            color: var(--text-light);
        }

        .journal-entry-category {
            font-size: 0.8rem;
            background-color: var(--accent-color);
            padding: 2px 8px;
            border-radius: 12px;
        }

        .journal-entry-title {
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
        }

        .journal-entry-content {
            margin-bottom: var(--spacing-sm);
        }

        .journal-entry-actions {
            display: flex;
            gap: var(--spacing-sm);
            justify-content: flex-end;
        }

        .journal-entry-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .journal-entry-actions button:hover {
            color: var(--primary-color);
        }

        .journal-entry-actions .delete-btn:hover {
            color: var(--error-color);
        }

        .no-entries {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--text-light);
        }

        /* Mobile Sidebar Toggle */
        .sidebar-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white);
            border: none;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow: var(--shadow-md);
            z-index: 100;
            cursor: pointer;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .dashboard-sidebar {
                position: fixed;
                left: -250px;
                z-index: 1000;
            }

            .dashboard-sidebar.active {
                left: 0;
            }

            .sidebar-toggle {
                display: flex;
            }

            .dashboard-main {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <h1><i class="fas fa-baby"></i> MATERNIFY</h1>
            </div>
            <nav>
                <button class="mobile-menu-btn">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
                <ul class="nav-menu">
                    <li><a href="/">Home</a></li>
                    <li>
                        <div class="user-menu">
                            <span class="user-greeting">Hello, <span id="nav-user-name">User</span>!</span>
                            <a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Mobile Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="dashboardSidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">MATERNIFY</h3>
                <div class="sidebar-user">
                    <div class="user-avatar">
                        <span id="user-initial">U</span>
                    </div>
                    <div class="user-info">
                        <div class="user-name" id="sidebar-user-name">User</div>
                        <div class="user-role">Member</div>
                    </div>
                </div>
            </div>

            <div class="sidebar-nav">
                <h4 class="sidebar-nav-title">Navigation</h4>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/pages/dashboard.html" class="sidebar-menu-link">
                            <span class="sidebar-menu-icon"><i class="fas fa-home"></i></span>
                            Dashboard
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/pages/profile.html" class="sidebar-menu-link">
                            <span class="sidebar-menu-icon"><i class="fas fa-user"></i></span>
                            My Profile
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/pages/health-journal.html" class="sidebar-menu-link active">
                            <span class="sidebar-menu-icon"><i class="fas fa-notes-medical"></i></span>
                            Health Journal
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-nav">
                <h4 class="sidebar-nav-title">Resources</h4>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="/pages/pregnancy-care.html" class="sidebar-menu-link">
                            <span class="sidebar-menu-icon"><i class="fas fa-female"></i></span>
                            Pregnancy Care
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/pages/infant-care.html" class="sidebar-menu-link">
                            <span class="sidebar-menu-icon"><i class="fas fa-baby"></i></span>
                            Infant Care
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/pages/tools.html" class="sidebar-menu-link">
                            <span class="sidebar-menu-icon"><i class="fas fa-tools"></i></span>
                            Tools
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/pages/community.html" class="sidebar-menu-link">
                            <span class="sidebar-menu-icon"><i class="fas fa-users"></i></span>
                            Community
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="/pages/blog.html" class="sidebar-menu-link">
                            <span class="sidebar-menu-icon"><i class="fas fa-book-open"></i></span>
                            Articles
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <a href="#" class="logout-btn" id="sidebarLogoutBtn">
                    <span class="logout-icon"><i class="fas fa-sign-out-alt"></i></span>
                    Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <div class="journal-header">
                <div>
                    <h1 class="journal-title">Health Journal</h1>
                    <p class="journal-subtitle">Track your pregnancy and baby's health journey</p>
                </div>
                <div class="journal-actions">
                    <button id="newEntryBtn" class="btn btn-primary"><i class="fas fa-plus"></i> New Entry</button>
                    <button id="exportJournalBtn" class="btn btn-secondary"><i class="fas fa-download"></i> Export</button>
                </div>
            </div>

            <div class="journal-container">
                <div class="journal-tabs">
                    <div class="journal-tab active" data-tab="all">All Entries</div>
                    <div class="journal-tab" data-tab="symptoms">Symptoms</div>
                    <div class="journal-tab" data-tab="appointments">Appointments</div>
                    <div class="journal-tab" data-tab="measurements">Measurements</div>
                    <div class="journal-tab" data-tab="notes">General Notes</div>
                </div>

                <div class="journal-content">
                    <div id="journalForm" class="journal-form" style="display: none;">
                        <h3>New Journal Entry</h3>
                        <form id="healthJournalForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="entryDate">Date</label>
                                    <input type="date" id="entryDate" name="entryDate" required>
                                </div>
                                <div class="form-group">
                                    <label for="entryCategory">Category</label>
                                    <select id="entryCategory" name="entryCategory" required>
                                        <option value="">Select a category</option>
                                        <option value="symptoms">Symptoms</option>
                                        <option value="appointments">Appointments</option>
                                        <option value="measurements">Measurements</option>
                                        <option value="notes">General Notes</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="entryTitle">Title</label>
                                    <input type="text" id="entryTitle" name="entryTitle" placeholder="Enter a title for your entry" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="entryContent">Description</label>
                                    <textarea id="entryContent" name="entryContent" placeholder="Write your journal entry here..." required></textarea>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="cancelEntryBtn" class="btn btn-secondary">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save Entry</button>
                            </div>
                        </form>
                    </div>

                    <div id="journalEntries" class="journal-entries">
                        <!-- Journal entries will be displayed here -->
                        <div class="no-entries" id="noEntries">
                            <i class="fas fa-book fa-3x"></i>
                            <h3>No journal entries yet</h3>
                            <p>Start tracking your health journey by adding your first entry.</p>
                            <button id="startJournalBtn" class="btn btn-primary">Create First Entry</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <h3><i class="fas fa-baby"></i> MATERNIFY</h3>
                    <p>Supporting parents through every step of their pregnancy and infant care journey with expert guidance and tools.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-pinterest"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/pages/pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="/pages/infant-care.html">Infant Care</a></li>
                        <li><a href="/pages/tools.html">Tools</a></li>
                        <li><a href="/pages/community.html">Community</a></li>
                        <li><a href="/pages/blog.html">Articles</a></li>
                    </ul>
                </div>
                <div class="footer-tools">
                    <h4>Useful Tools</h4>
                    <ul>
                        <li><a href="/pages/tools.html#due-date">Due Date Calculator</a></li>
                        <li><a href="/pages/tools.html#bmi">BMI Tracker</a></li>
                        <li><a href="/pages/tools.html#growth-tracker">Growth Tracker</a></li>
                        <li><a href="/pages/tools.html#vaccination">Vaccination Schedule</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for the latest articles and updates.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" required>
                        <button type="submit" class="btn btn-sm">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MATERNIFY. All rights reserved.</p>
                <ul class="footer-bottom-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Accessibility</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script src="/js/main.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/api-service.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/logout.js"></script>
    <script src="/js/health-journal.js"></script>
