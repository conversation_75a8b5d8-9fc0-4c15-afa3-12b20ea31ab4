/**
 * Video Resources functionality for MATERNIFY
 * This script handles the video resources page including tab switching, video display, and modal functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Video Resources script loaded');

    // Video data - in a real application, this would come from a database
    const videoData = [
        {
            id: 1,
            title: "First Trimester Essentials: What to Expect",
            description: "A comprehensive guide to the first trimester of pregnancy, covering common symptoms, important nutrition guidelines, and prenatal care recommendations.",
            thumbnail: "https://img.youtube.com/vi/-LHNfkaaMFA/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/-LHNfkaaMFA", // First trimester video
            category: "pregnancy",
            duration: "10:01",
            resources: [
                { title: "First Trimester Nutrition Guide", url: "/pages/pregnancy-care.html#first-trimester" },
                { title: "Morning Sickness Management", url: "/pages/pregnancy-care.html#first-trimester" }
            ]
        },
        {
            id: 2,
            title: "Safe Exercises During Pregnancy",
            description: "Learn about safe and effective exercises that can be performed during each trimester of pregnancy to maintain fitness and prepare for childbirth.",
            thumbnail: "https://img.youtube.com/vi/7fDo4tBQdFI/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/7fDo4tBQdFI", // Safe exercises during pregnancy video
            category: "pregnancy",
            duration: "10:27",
            resources: [
                { title: "Pregnancy Exercise Guidelines", url: "/pages/pregnancy-care.html#second-trimester" },
                { title: "Prenatal Yoga Benefits", url: "/pages/blog.html" }
            ]
        },
        {
            id: 3,
            title: "Understanding Labor and Delivery",
            description: "A detailed explanation of the labor and delivery process, including the stages of labor, pain management options, and what to expect during childbirth.",
            thumbnail: "https://img.youtube.com/vi/dYu-0rOnLpA/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/dYu-0rOnLpA", // Labor and delivery video
            category: "childbirth",
            duration: "8:32",
            resources: [
                { title: "Birth Plan Template", url: "/pages/pregnancy-care.html#third-trimester" },
                { title: "Pain Management Options", url: "/pages/blog.html" }
            ]
        },
        {
            id: 4,
            title: "Newborn Care Basics",
            description: "Essential information for new parents on caring for a newborn, including feeding, bathing, diapering, and recognizing common health concerns.",
            thumbnail: "https://img.youtube.com/vi/WQuX_Yw3yIg/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/WQuX_Yw3yIg", // Newborn care basics video
            category: "newborn",
            duration: "10:05",
            resources: [
                { title: "Newborn Care Guide", url: "/pages/infant-care.html" },
                { title: "Diapering Techniques", url: "/pages/infant-care.html" }
            ]
        },
        {
            id: 5,
            title: "Breastfeeding Techniques and Tips",
            description: "Expert advice on breastfeeding techniques, proper latch, common challenges, and tips for successful breastfeeding.",
            thumbnail: "https://img.youtube.com/vi/-Ds3QW3CWJ0/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/-Ds3QW3CWJ0", // Breastfeeding techniques video
            category: "breastfeeding",
            duration: "8:14",
            resources: [
                { title: "Breastfeeding Guide", url: "/pages/infant-care.html#breastfeeding" },
                { title: "Common Breastfeeding Challenges", url: "/pages/blog.html" }
            ]
        },
        {
            id: 6,
            title: "Baby Sleep Patterns and Routines",
            description: "Understanding infant sleep patterns and establishing healthy sleep routines for your baby from newborn to 12 months.",
            thumbnail: "https://img.youtube.com/vi/j0M4v24gSaw/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/j0M4v24gSaw", // Baby sleep patterns video
            category: "infant",
            duration: "9:58",
            resources: [
                { title: "Infant Sleep Guide", url: "/pages/infant-care.html" },
                { title: "Sleep Training Methods", url: "/pages/blog.html" }
            ]
        },
        {
            id: 7,
            title: "Postpartum Recovery and Self-Care",
            description: "Guidance on physical and emotional recovery after childbirth, including self-care strategies for new mothers.",
            thumbnail: "https://img.youtube.com/vi/Ybyo5nRmr1Y/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/Ybyo5nRmr1Y", // Postpartum recovery video
            category: "wellness",
            duration: "10:12",
            resources: [
                { title: "Postpartum Recovery Tips", url: "/pages/blog.html" },
                { title: "Self-Care for New Moms", url: "/pages/blog.html" }
            ]
        },
        {
            id: 8,
            title: "Infant Developmental Milestones",
            description: "Learn about the key developmental milestones during your baby's first year and how to support their growth and development.",
            thumbnail: "https://img.youtube.com/vi/TtnhsOAG7vI/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/TtnhsOAG7vI", // Baby development milestones video
            category: "infant",
            duration: "5:01",
            resources: [
                { title: "Developmental Milestones Chart", url: "/pages/infant-care.html" },
                { title: "Supporting Baby's Development", url: "/pages/blog.html" }
            ]
        },
        {
            id: 9,
            title: "Preparing for a C-Section",
            description: "Information on what to expect before, during, and after a cesarean delivery, including recovery tips and care guidelines.",
            thumbnail: "https://img.youtube.com/vi/Gl_PzPraWS8/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/Gl_PzPraWS8", // C-section video
            category: "childbirth",
            duration: "9:45",
            resources: [
                { title: "C-Section Recovery Guide", url: "/pages/blog.html" },
                { title: "Birth Plan for C-Section", url: "/pages/pregnancy-care.html#third-trimester" }
            ]
        },
        {
            id: 10,
            title: "Introducing Solid Foods to Your Baby",
            description: "Guidelines on when and how to introduce solid foods to your baby, including recommended first foods and feeding techniques.",
            thumbnail: "https://img.youtube.com/vi/LAfn4s8Jcps/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/LAfn4s8Jcps", // Introducing solid foods video
            category: "infant",
            duration: "5:32",
            resources: [
                { title: "Solid Food Introduction Guide", url: "/pages/infant-care.html" },
                { title: "Baby-Led Weaning vs. Purees", url: "/pages/blog.html" }
            ]
        },
        {
            id: 11,
            title: "Prenatal Yoga for Each Trimester",
            description: "Safe and effective prenatal yoga poses and sequences for each trimester to improve flexibility, reduce discomfort, and prepare for childbirth.",
            thumbnail: "https://img.youtube.com/vi/OT7te9Oncpk/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/OT7te9Oncpk", // Prenatal yoga video
            category: "wellness",
            duration: "15:01",
            resources: [
                { title: "Prenatal Exercise Guidelines", url: "/pages/pregnancy-care.html" },
                { title: "Benefits of Yoga During Pregnancy", url: "/pages/blog.html" }
            ]
        },
        {
            id: 12,
            title: "Understanding Baby Cues and Communication",
            description: "Learn to recognize and respond to your baby's cues and early communication signals to strengthen your bond and meet their needs.",
            thumbnail: "https://img.youtube.com/vi/wTOh1gXEiBg/maxresdefault.jpg",
            videoUrl: "https://www.youtube.com/embed/wTOh1gXEiBg", // Baby cues and communication video
            category: "newborn",
            duration: "4:23",
            resources: [
                { title: "Newborn Communication Guide", url: "/pages/infant-care.html" },
                { title: "Responsive Parenting", url: "/pages/blog.html" }
            ]
        }
    ];

    // DOM Elements
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    const videoModal = document.getElementById('videoModal');
    const modalClose = document.getElementById('modalClose');
    const videoFrame = document.getElementById('videoFrame');
    const modalTitle = document.getElementById('modalTitle');
    const modalCategory = document.getElementById('modalCategory');
    const modalDuration = document.getElementById('modalDuration');
    const modalDescription = document.getElementById('modalDescription');
    const modalResources = document.getElementById('modalResources');

    // Initialize the page
    initializePage();

    // Tab switching functionality
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Show corresponding content
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Modal close functionality
    modalClose.addEventListener('click', function() {
        closeVideoModal();
    });

    // Close modal when clicking outside the content
    videoModal.addEventListener('click', function(e) {
        if (e.target === videoModal) {
            closeVideoModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && videoModal.classList.contains('active')) {
            closeVideoModal();
        }
    });

    /**
     * Initialize the page by populating video cards
     */
    function initializePage() {
        // Populate All Videos tab
        const allVideosGrid = document.querySelector('#all-videos .video-grid');
        allVideosGrid.innerHTML = '';
        videoData.forEach(video => {
            allVideosGrid.appendChild(createVideoCard(video));
        });

        // Populate category-specific tabs
        const categories = ['pregnancy', 'childbirth', 'newborn', 'infant', 'breastfeeding', 'wellness'];
        categories.forEach(category => {
            const categoryVideos = videoData.filter(video => video.category === category);
            const categoryGrid = document.querySelector(`#${category}-videos .video-grid`);
            categoryGrid.innerHTML = '';
            categoryVideos.forEach(video => {
                categoryGrid.appendChild(createVideoCard(video));
            });
        });
    }

    /**
     * Create a video card element
     */
    function createVideoCard(video) {
        const card = document.createElement('div');
        card.className = 'video-card';
        card.setAttribute('data-video-id', video.id);

        card.innerHTML = `
            <div class="video-thumbnail">
                <img src="${video.thumbnail}" alt="${video.title}">
                <div class="play-button">
                    <i class="fas fa-play"></i>
                </div>
            </div>
            <div class="video-info">
                <h3 class="video-title">${video.title}</h3>
                <p class="video-description">${truncateText(video.description, 100)}</p>
                <div class="video-meta">
                    <span class="video-category">${formatCategory(video.category)}</span>
                    <span class="video-duration"><i class="far fa-clock"></i> ${video.duration}</span>
                </div>
            </div>
        `;

        // Add click event to open modal
        card.addEventListener('click', function() {
            openVideoModal(video);
        });

        return card;
    }

    /**
     * Open the video modal with the selected video
     */
    function openVideoModal(video) {
        // Set modal content
        videoFrame.src = video.videoUrl;
        modalTitle.textContent = video.title;
        modalCategory.textContent = formatCategory(video.category);
        modalDuration.innerHTML = `<i class="far fa-clock"></i> ${video.duration}`;
        modalDescription.textContent = video.description;

        // Set related resources
        modalResources.innerHTML = '';
        video.resources.forEach(resource => {
            const li = document.createElement('li');
            li.innerHTML = `<a href="${resource.url}">${resource.title}</a>`;
            modalResources.appendChild(li);
        });

        // Show modal
        videoModal.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent scrolling
    }

    /**
     * Close the video modal
     */
    function closeVideoModal() {
        videoModal.classList.remove('active');
        videoFrame.src = ''; // Stop video playback
        document.body.style.overflow = ''; // Restore scrolling
    }

    /**
     * Format category name for display
     */
    function formatCategory(category) {
        const categoryMap = {
            'pregnancy': 'Pregnancy',
            'childbirth': 'Childbirth',
            'newborn': 'Newborn Care',
            'infant': 'Infant Development',
            'breastfeeding': 'Breastfeeding',
            'wellness': 'Maternal Wellness'
        };
        return categoryMap[category] || category.charAt(0).toUpperCase() + category.slice(1);
    }

    /**
     * Truncate text to a specified length
     */
    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
});
