API Tests started at 2025-05-16T09:24:30.864Z
Testing registration API...
Registration response: {
  "statusCode": 201,
  "headers": {
    "x-powered-by": "Express",
    "content-type": "application/json; charset=utf-8",
    "content-length": "283",
    "etag": "W/\"11b-zc3m3t3oCsriQAFApmi/OT3zzS8\"",
    "date": "Fri, 16 May 2025 09:24:30 GMT",
    "connection": "keep-alive",
    "keep-alive": "timeout=5"
  },
  "data": {
    "success": true,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEyMzQ1Njc4OSIsImlhdCI6MTc0NzM4NzQ3MCwiZXhwIjoxNzQ5OTc5NDcwfQ.3jC5wJ-ZZBQlldvFR-XVLUcbBvF5thpUZsX2ML4YjaA",
    "user": {
      "_id": "123456789",
      "firstName": "Test",
      "lastName": "User",
      "email": "<EMAIL>",
      "role": "user"
    }
  }
}
Testing login API...
Login response: {
  "statusCode": 200,
  "headers": {
    "x-powered-by": "Express",
    "content-type": "application/json; charset=utf-8",
    "content-length": "283",
    "etag": "W/\"11b-zc3m3t3oCsriQAFApmi/OT3zzS8\"",
    "date": "Fri, 16 May 2025 09:24:30 GMT",
    "connection": "keep-alive",
    "keep-alive": "timeout=5"
  },
  "data": {
    "success": true,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEyMzQ1Njc4OSIsImlhdCI6MTc0NzM4NzQ3MCwiZXhwIjoxNzQ5OTc5NDcwfQ.3jC5wJ-ZZBQlldvFR-XVLUcbBvF5thpUZsX2ML4YjaA",
    "user": {
      "_id": "123456789",
      "firstName": "Test",
      "lastName": "User",
      "email": "<EMAIL>",
      "role": "user"
    }
  }
}
API Tests completed at 2025-05-16T09:24:30.919Z
